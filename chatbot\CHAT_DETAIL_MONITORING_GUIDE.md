# 会话详情页监听功能使用指南

## 概述

新增的会话详情页监听功能可以自动检测用户是否处在会话详情页，当检测到进入会话详情页时，会自动获取会话概要并在日志中显示。这个功能特别适合需要实时监控会话状态的场景。

## 核心功能

### 1. 自动监听
- **实时检测**: 定期检查是否在会话详情页
- **状态变化检测**: 检测进入/离开会话详情页的状态变化
- **内容变化检测**: 检测会话内容的变化
- **自动获取概要**: 进入会话详情页时自动获取会话概要

### 2. 日志显示
- **详细日志**: 在日志中显示完整的会话概要
- **统计信息**: 显示消息数量等统计信息
- **状态跟踪**: 记录监听状态变化

## API 方法

### 启用监听
```kotlin
/**
 * 启用会话详情页监听
 * @param intervalMs 监听间隔时间（毫秒），默认2000ms
 */
fun enableChatDetailPageMonitoring(intervalMs: Long = 2000L)
```

### 禁用监听
```kotlin
/**
 * 禁用会话详情页监听
 */
fun disableChatDetailPageMonitoring()
```

### 检查监听状态
```kotlin
/**
 * 检查监听状态
 */
fun isChatDetailPageMonitoringEnabled(): Boolean
```

### 设置监听间隔
```kotlin
/**
 * 设置监听间隔
 */
fun setChatDetailPageMonitoringInterval(intervalMs: Long)
```

## 使用示例

### 基本使用
```kotlin
val customerServiceHandler = CustomerServiceHandler(context)
customerServiceHandler.setAccessibilityService(accessibilityService)

// 启用监听，每2秒检查一次
customerServiceHandler.enableChatDetailPageMonitoring(2000L)

// 监听器会自动工作，无需额外代码
// 当进入会话详情页时，会自动在日志中显示会话概要
```

### 在AccessibilityService中使用
```kotlin
class MyAccessibilityService : AccessibilityService() {
    private lateinit var customerServiceHandler: CustomerServiceHandler

    override fun onServiceConnected() {
        super.onServiceConnected()
        
        // 初始化
        customerServiceHandler = CustomerServiceHandler(this)
        customerServiceHandler.setAccessibilityService(this)
        
        // 启用会话详情页监听
        customerServiceHandler.enableChatDetailPageMonitoring(2000L)
        Log.d("Service", "✅ 会话详情页监听已启用")
    }

    override fun onAccessibilityEvent(event: AccessibilityEvent?) {
        // 监听器会自动处理会话详情页检测
        // 这里可以处理其他事件
    }

    override fun onDestroy() {
        super.onDestroy()
        
        // 清理资源（会自动禁用监听）
        customerServiceHandler.cleanup()
    }
}
```

### 动态控制监听
```kotlin
// 检查当前监听状态
if (customerServiceHandler.isChatDetailPageMonitoringEnabled()) {
    Log.d("Monitor", "监听正在运行")
    
    // 暂时禁用监听
    customerServiceHandler.disableChatDetailPageMonitoring()
    
    // 3秒后重新启用，使用更短的间隔
    Handler().postDelayed({
        customerServiceHandler.enableChatDetailPageMonitoring(1000L)
    }, 3000)
}

// 调整监听间隔
customerServiceHandler.setChatDetailPageMonitoringInterval(5000L) // 5秒间隔
```

## 日志输出示例

当监听器检测到进入会话详情页时，会在日志中显示类似以下内容：

```
D/CustomerServiceHandler: 🎯 === 监听检测：进入会话详情页 ===
D/CustomerServiceHandler: 📋 正在获取会话概要...
D/CustomerServiceHandler: ✅ 找到会话记录容器
D/CustomerServiceHandler: 📝 会话记录包含 15 条文本内容
D/CustomerServiceHandler: ✅ 会话概要获取成功
D/CustomerServiceHandler: 📝 === 会话记录概要 ===
D/CustomerServiceHandler: 会话记录总行数: 15
D/CustomerServiceHandler: [0] - 客户: 你好，我想咨询一下产品信息
D/CustomerServiceHandler: [1] - 客服: 您好！很高兴为您服务
D/CustomerServiceHandler: [2] - 客户: 这个产品的价格是多少？
D/CustomerServiceHandler: [3] - 客服: 产品价格是299元
D/CustomerServiceHandler: [4] - 客户: 有优惠活动吗？
D/CustomerServiceHandler: 📊 统计信息: 共 5 条消息记录
D/CustomerServiceHandler: 📝 === 会话记录概要结束 ===
```

当会话内容发生变化时：
```
D/CustomerServiceHandler: 🔄 === 监听检测：会话内容发生变化 ===
D/CustomerServiceHandler: 📝 === 更新的会话记录概要 ===
D/CustomerServiceHandler: [5] - 客服: 现在有8折优惠活动
D/CustomerServiceHandler: 📊 统计信息: 共 6 条消息记录
D/CustomerServiceHandler: 📝 === 更新的会话记录概要结束 ===
```

## 性能考虑

### 监听间隔设置
- **高频监听**: 1000ms - 适合需要快速响应的场景
- **标准监听**: 2000ms - 平衡性能和响应速度（推荐）
- **低频监听**: 5000ms - 适合后台监控场景

### 资源管理
- 监听器会自动管理AccessibilityNodeInfo资源
- 在不需要时及时禁用监听以节省系统资源
- cleanup()方法会自动停止所有监听

## 注意事项

1. **自动化**: 启用监听后无需手动调用检查方法
2. **状态检测**: 监听器会自动检测进入/离开会话详情页
3. **内容变化**: 会自动检测会话内容的变化
4. **资源清理**: 记得在适当时候调用cleanup()方法
5. **日志级别**: 使用Log.d输出详细信息，可根据需要调整日志级别

## 与手动检查的区别

| 功能 | 监听模式 | 手动检查模式 |
|------|----------|--------------|
| 检测方式 | 自动定期检查 | 需要手动调用 |
| 状态变化 | 自动检测 | 需要自己比较 |
| 内容变化 | 自动检测 | 需要自己比较 |
| 资源消耗 | 持续但可控 | 按需消耗 |
| 使用场景 | 实时监控 | 特定时机检查 |

## 最佳实践

1. **合理设置间隔**: 根据实际需求设置监听间隔
2. **及时清理**: 在不需要时禁用监听
3. **日志管理**: 在生产环境中可以调整日志级别
4. **异常处理**: 监听器内置了完整的异常处理
5. **状态查询**: 使用isChatDetailPageMonitoringEnabled()查询状态

这个监听功能让您可以轻松实现会话详情页的实时监控，无需手动编写复杂的检测逻辑。
