{"logs": [{"outputFile": "com.coffee.chatbot.app-mergeDebugResources-47:/values-sv/values-sv.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\8cb4d84fca6908b8a011123061ad45a8\\transformed\\core-1.13.1\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,350,449,557,662,783", "endColumns": "94,101,97,98,107,104,120,100", "endOffsets": "145,247,345,444,552,657,778,879"}, "to": {"startLines": "2,3,4,5,6,7,8,81", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,200,302,400,499,607,712,8365", "endColumns": "94,101,97,98,107,104,120,100", "endOffsets": "195,297,395,494,602,707,828,8461"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\9495e8a9cf20fc000420e72f347dde11\\transformed\\foundation-release\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,145", "endColumns": "89,88", "endOffsets": "140,229"}, "to": {"startLines": "85,86", "startColumns": "4,4", "startOffsets": "8731,8821", "endColumns": "89,88", "endOffsets": "8816,8905"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f11a7a2dfc998ec016db27aaa3914b46\\transformed\\material3-release\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,194,327,434,566,682,778,891,1035,1159,1314,1399,1498,1588,1682,1796,1918,2022,2155,2282,2417,2589,2717,2835,2961,3081,3172,3270,3388,3527,3623,3731,3834,3967,4110,4216,4313,4393,4491,4583,4699,4783,4868,4969,5049,5134,5233,5333,5428,5528,5615,5719,5820,5924,6046,6126,6230", "endColumns": "138,132,106,131,115,95,112,143,123,154,84,98,89,93,113,121,103,132,126,134,171,127,117,125,119,90,97,117,138,95,107,102,132,142,105,96,79,97,91,115,83,84,100,79,84,98,99,94,99,86,103,100,103,121,79,103,98", "endOffsets": "189,322,429,561,677,773,886,1030,1154,1309,1394,1493,1583,1677,1791,1913,2017,2150,2277,2412,2584,2712,2830,2956,3076,3167,3265,3383,3522,3618,3726,3829,3962,4105,4211,4308,4388,4486,4578,4694,4778,4863,4964,5044,5129,5228,5328,5423,5523,5610,5714,5815,5919,6041,6121,6225,6324"}, "to": {"startLines": "16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1461,1600,1733,1840,1972,2088,2184,2297,2441,2565,2720,2805,2904,2994,3088,3202,3324,3428,3561,3688,3823,3995,4123,4241,4367,4487,4578,4676,4794,4933,5029,5137,5240,5373,5516,5622,5719,5799,5897,5989,6105,6189,6274,6375,6455,6540,6639,6739,6834,6934,7021,7125,7226,7330,7452,7532,7636", "endColumns": "138,132,106,131,115,95,112,143,123,154,84,98,89,93,113,121,103,132,126,134,171,127,117,125,119,90,97,117,138,95,107,102,132,142,105,96,79,97,91,115,83,84,100,79,84,98,99,94,99,86,103,100,103,121,79,103,98", "endOffsets": "1595,1728,1835,1967,2083,2179,2292,2436,2560,2715,2800,2899,2989,3083,3197,3319,3423,3556,3683,3818,3990,4118,4236,4362,4482,4573,4671,4789,4928,5024,5132,5235,5368,5511,5617,5714,5794,5892,5984,6100,6184,6269,6370,6450,6535,6634,6734,6829,6929,7016,7120,7221,7325,7447,7527,7631,7730"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\07f0c92abaf5e3cb600506e85eba9207\\transformed\\ui-release\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,286,382,481,569,645,733,822,903,989,1079,1148,1222,1293,1363,1441,1508", "endColumns": "92,87,95,98,87,75,87,88,80,85,89,68,73,70,69,77,66,119", "endOffsets": "193,281,377,476,564,640,728,817,898,984,1074,1143,1217,1288,1358,1436,1503,1623"}, "to": {"startLines": "9,10,11,12,13,14,15,73,74,75,76,77,78,79,80,82,83,84", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "833,926,1014,1110,1209,1297,1373,7735,7824,7905,7991,8081,8150,8224,8295,8466,8544,8611", "endColumns": "92,87,95,98,87,75,87,88,80,85,89,68,73,70,69,77,66,119", "endOffsets": "921,1009,1105,1204,1292,1368,1456,7819,7900,7986,8076,8145,8219,8290,8360,8539,8606,8726"}}]}]}