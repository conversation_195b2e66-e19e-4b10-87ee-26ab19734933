package com.coffee.chatbot.service

import android.content.Context
import android.util.Log
import com.google.gson.Gson
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody.Companion.toRequestBody
import java.io.IOException
import java.util.concurrent.TimeUnit

class OpenAIService(private val context: Context) {

    private val client = OkHttpClient.Builder()
        .connectTimeout(60, TimeUnit.SECONDS)
        .writeTimeout(60, TimeUnit.SECONDS)
        .readTimeout(60, TimeUnit.SECONDS)
        .build()
    private val gson = Gson()

    private fun getSettings(): Quad<String, String, String, String> {
        val sharedPreferences = context.getSharedPreferences("ChatbotPrefs", Context.MODE_PRIVATE)
        val baseUrl = sharedPreferences.getString("BASE_URL", "https://api.openai.com/v1") ?: "https://api.openai.com/v1"
        val modelName = sharedPreferences.getString("MODEL_NAME", "gpt-4") ?: "gpt-4"
        val systemPrompt = sharedPreferences.getString("SYSTEM_PROMPT", "You are a helpful assistant.") ?: "You are a helpful assistant."
        val apiKey = sharedPreferences.getString("API_KEY", "") ?: ""
        return Quad(baseUrl, modelName, systemPrompt, apiKey)
    }

    suspend fun getCompletion(prompt: String): String? {
        return withContext(Dispatchers.IO) {
            val (baseUrl, modelName, systemPrompt, apiKey) = getSettings()

            if (apiKey.isBlank()) {
                Log.e("OpenAIService", "API Key is not set in the app settings.")
                return@withContext "错误：API Key 未设置"
            }

            val requestBody = OpenAIChatRequest(
                model = modelName,
                messages = listOf(
                    OpenAIMessage("system", systemPrompt),
                    OpenAIMessage("user", prompt)
                )
            )

            val requestJson = gson.toJson(requestBody)
            val body = requestJson.toRequestBody("application/json; charset=utf-f-8".toMediaType())

            val request = Request.Builder()
                .url("$baseUrl/chat/completions")
                .header("Authorization", "Bearer $apiKey")
                .post(body)
                .build()

            try {
                client.newCall(request).execute().use { response ->
                    if (!response.isSuccessful) {
                        val errorBody = response.body?.string()
                        Log.e("OpenAIService", "API Error: ${response.code} - $errorBody")
                        return@withContext "API 请求失败: ${response.code}"
                    }

                    val responseBody = response.body?.string()
                    val chatResponse = gson.fromJson(responseBody, OpenAIChatResponse::class.java)
                    chatResponse.choices.firstOrNull()?.message?.content
                }
            } catch (e: IOException) {
                Log.e("OpenAIService", "Network Error", e)
                "网络错误: ${e.message}"
            }
        }
    }
}

// Data classes for OpenAI API
data class OpenAIChatRequest(
    val model: String,
    val messages: List<OpenAIMessage>
)

data class OpenAIMessage(
    val role: String,
    val content: String
)

data class OpenAIChatResponse(
    val choices: List<Choice>
)

data class Choice(
    val message: OpenAIMessage
)

data class Quad<T1, T2, T3, T4>(val first: T1, val second: T2, val third: T3, val fourth: T4)