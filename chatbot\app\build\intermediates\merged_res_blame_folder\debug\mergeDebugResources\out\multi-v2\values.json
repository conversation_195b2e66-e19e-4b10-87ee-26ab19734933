{"logs": [{"outputFile": "com.coffee.chatbot.app-mergeDebugResources-47:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\0bc27dfedd5f741e488aee345ef3fb97\\transformed\\window-1.2.0\\res\\values\\values.xml", "from": {"startLines": "2,3,9,17,25,37,43,49,50,51,52,53,54,55,61,66,74,89", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,114,287,506,725,1039,1227,1414,1467,1527,1579,1624,1663,1723,1918,2076,2358,2972", "endLines": "2,8,16,24,36,42,48,49,50,51,52,53,54,60,65,73,88,104", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59,24,24,24,24,24", "endOffsets": "109,282,501,720,1034,1222,1409,1462,1522,1574,1619,1658,1718,1913,2071,2353,2967,3621"}, "to": {"startLines": "2,3,9,17,26,38,44,50,51,52,53,54,126,292,298,412,420,435", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,209,382,601,880,1194,1382,1569,1622,1682,1734,1779,6219,16864,17059,20982,21264,21878", "endLines": "2,8,16,24,37,43,49,50,51,52,53,54,126,297,302,419,434,450", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59,24,24,24,24,24", "endOffsets": "204,377,596,815,1189,1377,1564,1617,1677,1729,1774,1813,6274,17054,17212,21259,21873,22527"}}, {"source": "D:\\workspace\\gitee.com\\coffee\\bot\\chatbot\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "7,2,3,4,5,6,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "286,55,102,149,196,241,328", "endColumns": "41,46,46,46,44,44,41", "endOffsets": "323,97,144,191,236,281,365"}, "to": {"startLines": "57,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "1978,2283,2330,2377,2424,2469,2514", "endColumns": "41,46,46,46,44,44,41", "endOffsets": "2015,2325,2372,2419,2464,2509,2551"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\0812e207275b95c4055ee4a09cd23f0c\\transformed\\lifecycle-runtime-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "153", "startColumns": "4", "startOffsets": "7656", "endColumns": "42", "endOffsets": "7694"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\07f0c92abaf5e3cb600506e85eba9207\\transformed\\ui-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,61,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,209,268,328,388,448,508,568,628,688,748,808,868,927,987,1047,1107,1167,1227,1287,1347,1407,1467,1527,1586,1646,1706,1765,1824,1883,1942,2001,2060,2134,2192,2247,2298,2353,2406,2471,2525,2591,2692,2750,2802,2862,2924,2978,3028,3082,3128,3174,3216,3256,3303,3339,3429,3541,3652", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,60,63,67", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,45,41,39,46,35,89,12,12,12", "endOffsets": "204,263,323,383,443,503,563,623,683,743,803,863,922,982,1042,1102,1162,1222,1282,1342,1402,1462,1522,1581,1641,1701,1760,1819,1878,1937,1996,2055,2129,2187,2242,2293,2348,2401,2466,2520,2586,2687,2745,2797,2857,2919,2973,3023,3077,3123,3169,3211,3251,3298,3334,3424,3536,3647,3842"}, "to": {"startLines": "93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,127,128,130,131,157,170,171,172,173,174,176,177,239,240,241,242,243,245,246,247,250,251,252,255,271,274", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4235,4294,4353,4413,4473,4533,4593,4653,4713,4773,4833,4893,4953,5012,5072,5132,5192,5252,5312,5372,5432,5492,5552,5612,5671,5731,5791,5850,5909,5968,6027,6086,6145,6279,6337,6458,6509,7863,8785,8850,8904,8970,9071,9200,9252,13753,13815,13869,13919,13973,14067,14113,14155,14313,14360,14396,14597,15577,15688", "endLines": "93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,127,128,130,131,157,170,171,172,173,174,176,177,239,240,241,242,243,245,246,247,250,251,252,257,273,277", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,45,41,39,46,35,89,12,12,12", "endOffsets": "4289,4348,4408,4468,4528,4588,4648,4708,4768,4828,4888,4948,5007,5067,5127,5187,5247,5307,5367,5427,5487,5547,5607,5666,5726,5786,5845,5904,5963,6022,6081,6140,6214,6332,6387,6504,6559,7911,8845,8899,8965,9066,9124,9247,9307,13810,13864,13914,13968,14014,14108,14150,14190,14355,14391,14481,14704,15683,15878"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b7b2f1341de8d077ec3300d4fb221a9c\\transformed\\customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,109", "endColumns": "53,66", "endOffsets": "104,171"}, "to": {"startLines": "132,135", "startColumns": "4,4", "startOffsets": "6564,6688", "endColumns": "53,66", "endOffsets": "6613,6750"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\adaffcca5a42f97557ea5314f9d10402\\transformed\\ui-graphics-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "65", "endOffsets": "116"}, "to": {"startLines": "129", "startColumns": "4", "startOffsets": "6392", "endColumns": "65", "endOffsets": "6453"}}, {"source": "D:\\workspace\\gitee.com\\coffee\\bot\\chatbot\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "2,1,5,3,4", "startColumns": "4,4,4,4,4", "startOffsets": "61,16,243,148,196", "endColumns": "86,44,70,47,46", "endOffsets": "143,56,309,191,238"}, "to": {"startLines": "160,162,175,244,249", "startColumns": "4,4,4,4,4", "startOffsets": "8059,8229,9129,14019,14266", "endColumns": "86,44,70,47,46", "endOffsets": "8141,8269,9195,14062,14308"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7c077c7fa91fcc97275a88ea73d7d855\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "161", "startColumns": "4", "startOffsets": "8146", "endColumns": "82", "endOffsets": "8224"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\8cb4d84fca6908b8a011123061ad45a8\\transformed\\core-1.13.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,155,176,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8840,9521,10203", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,154,175,208,214", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8835,9516,10198,10365"}, "to": {"startLines": "25,55,56,58,59,60,61,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,133,134,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,159,163,164,165,166,167,168,169,248,278,279,283,284,288,290,291,303,309,319,352,373,406", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "820,1818,1890,2020,2085,2151,2220,2556,2626,2694,2766,2836,2897,2971,3044,3105,3166,3228,3292,3354,3415,3483,3583,3643,3709,3782,3851,3908,3960,4022,4094,4170,6618,6653,6797,6852,6915,6970,7028,7086,7147,7210,7267,7318,7368,7429,7486,7552,7586,7621,7989,8274,8341,8413,8482,8551,8625,8697,14195,15883,16000,16201,16311,16512,16725,16797,17217,17420,17721,19452,20133,20815", "endLines": "25,55,56,58,59,60,61,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,133,134,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,159,163,164,165,166,167,168,169,248,278,282,283,287,288,290,291,308,318,351,372,405,411", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "875,1885,1973,2080,2146,2215,2278,2621,2689,2761,2831,2892,2966,3039,3100,3161,3223,3287,3349,3410,3478,3578,3638,3704,3777,3846,3903,3955,4017,4089,4165,4230,6648,6683,6847,6910,6965,7023,7081,7142,7205,7262,7313,7363,7424,7481,7547,7581,7616,7651,8054,8336,8408,8477,8546,8620,8692,8780,14261,15995,16196,16306,16507,16636,16792,16859,17415,17716,19447,20128,20810,20977"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\9495e8a9cf20fc000420e72f347dde11\\transformed\\foundation-release\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,111", "endColumns": "55,54", "endOffsets": "106,161"}, "to": {"startLines": "253,254", "startColumns": "4,4", "startOffsets": "14486,14542", "endColumns": "55,54", "endOffsets": "14537,14592"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\e800da8de34149e2d5d16dfe5c62e6bd\\transformed\\activity-1.8.2\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "136,154", "startColumns": "4,4", "startOffsets": "6755,7699", "endColumns": "41,59", "endOffsets": "6792,7754"}}, {"source": "D:\\workspace\\gitee.com\\coffee\\bot\\chatbot\\app\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "3", "startColumns": "4", "startOffsets": "56", "endColumns": "84", "endOffsets": "136"}, "to": {"startLines": "289", "startColumns": "4", "startOffsets": "16641", "endColumns": "83", "endOffsets": "16720"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d830c5d6522c44a74da798561b1b42c1\\transformed\\savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "155", "startColumns": "4", "startOffsets": "7759", "endColumns": "53", "endOffsets": "7808"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\308e36532ccdcd283b769249b2840395\\transformed\\lifecycle-viewmodel-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "156", "startColumns": "4", "startOffsets": "7813", "endColumns": "49", "endOffsets": "7858"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f11a7a2dfc998ec016db27aaa3914b46\\transformed\\material3-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,15,16,17,18,19,20,21,22,23,24,25,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,74", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,173,261,347,428,512,581,646,729,835,921,1041,1095,1164,1225,1294,1383,1478,1552,1649,1742,1840,1989,2080,2168,2264,2362,2426,2494,2581,2675,2742,2814,2886,2987,3096,3172,3241,3289,3355,3419,3493,3550,3607,3679,3729,3783,3854,3925,3995,4064,4122,4198,4269,4343,4429,4479,4549,4614,5329", "endLines": "2,3,4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,73,76", "endColumns": "72,87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,73,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64,12,12", "endOffsets": "168,256,342,423,507,576,641,724,830,916,1036,1090,1159,1220,1289,1378,1473,1547,1644,1737,1835,1984,2075,2163,2259,2357,2421,2489,2576,2670,2737,2809,2881,2982,3091,3167,3236,3284,3350,3414,3488,3545,3602,3674,3724,3778,3849,3920,3990,4059,4117,4193,4264,4338,4424,4474,4544,4609,5324,5477"}, "to": {"startLines": "158,178,179,180,181,182,183,184,185,186,187,190,191,192,193,194,195,196,197,198,199,200,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,258,268", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7916,9312,9400,9486,9567,9651,9720,9785,9868,9974,10060,10180,10234,10303,10364,10433,10522,10617,10691,10788,10881,10979,11128,11219,11307,11403,11501,11565,11633,11720,11814,11881,11953,12025,12126,12235,12311,12380,12428,12494,12558,12632,12689,12746,12818,12868,12922,12993,13064,13134,13203,13261,13337,13408,13482,13568,13618,13688,14709,15424", "endLines": "158,178,179,180,181,182,183,184,185,186,189,190,191,192,193,194,195,196,197,198,199,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,267,270", "endColumns": "72,87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,73,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64,12,12", "endOffsets": "7984,9395,9481,9562,9646,9715,9780,9863,9969,10055,10175,10229,10298,10359,10428,10517,10612,10686,10783,10876,10974,11123,11214,11302,11398,11496,11560,11628,11715,11809,11876,11948,12020,12121,12230,12306,12375,12423,12489,12553,12627,12684,12741,12813,12863,12917,12988,13059,13129,13198,13256,13332,13403,13477,13563,13613,13683,13748,15419,15572"}}]}]}