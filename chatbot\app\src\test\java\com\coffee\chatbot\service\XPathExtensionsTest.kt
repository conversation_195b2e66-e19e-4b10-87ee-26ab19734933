package com.coffee.chatbot.service

import android.view.accessibility.AccessibilityNodeInfo
import org.junit.Test
import org.junit.Assert.*
import org.mockito.Mockito.*

/**
 * XPath 扩展功能的单元测试
 */
class XPathExtensionsTest {

    /**
     * 创建模拟的 AccessibilityNodeInfo
     */
    private fun createMockNode(
        className: String = "android.widget.TextView",
        text: String? = null,
        id: String? = null,
        clickable: Boolean = false,
        childCount: Int = 0
    ): AccessibilityNodeInfo {
        val node = mock(AccessibilityNodeInfo::class.java)
        
        `when`(node.className).thenReturn(className)
        `when`(node.text).thenReturn(text?.let { android.text.SpannableString(it) })
        `when`(node.viewIdResourceName).thenReturn(id)
        `when`(node.isClickable).thenReturn(clickable)
        `when`(node.childCount).thenReturn(childCount)
        `when`(node.isEnabled).thenReturn(true)
        `when`(node.isVisibleToUser).thenReturn(true)
        
        return node
    }

    @Test
    fun testBasicXPathQuery() {
        // 创建模拟节点树
        val rootNode = createMockNode("android.widget.FrameLayout", childCount = 2)
        val textView = createMockNode("android.widget.TextView", text = "Hello", clickable = false)
        val button = createMockNode("android.widget.Button", text = "Click", clickable = true)
        
        `when`(rootNode.getChild(0)).thenReturn(textView)
        `when`(rootNode.getChild(1)).thenReturn(button)
        `when`(textView.parent).thenReturn(rootNode)
        `when`(button.parent).thenReturn(rootNode)
        
        // 测试 XPath 查询
        val result = rootNode.xpath("//TextView")
        assertNotNull(result)
        assertTrue(result.exists())
    }

    @Test
    fun testConvenientFindMethods() {
        val rootNode = createMockNode("android.widget.FrameLayout", childCount = 1)
        val button = createMockNode("android.widget.Button", text = "Send", clickable = true)
        
        `when`(rootNode.getChild(0)).thenReturn(button)
        `when`(button.parent).thenReturn(rootNode)
        
        // 测试便捷查找方法
        val foundButton = rootNode.find(className = "Button", text = "Send", clickable = true)
        // 注意：由于我们使用的是模拟对象，实际的查找逻辑可能不会完全工作
        // 这里主要测试方法调用不会抛出异常
        assertNotNull("Find method should not throw exception", foundButton)
    }

    @Test
    fun testXPathResultChaining() {
        val rootNode = createMockNode("android.widget.FrameLayout")
        
        // 测试链式调用不会抛出异常
        val result = rootNode.xpath("//TextView")
                            .parent()
                            .xpath(".//Button")
        
        assertNotNull(result)
        assertFalse(result.exists()) // 模拟环境下应该为空
    }

    @Test
    fun testCssSelectorConversion() {
        val rootNode = createMockNode("android.widget.FrameLayout")
        
        // 测试 CSS 选择器转换
        val result = rootNode.select("Button.clickable")
        assertNotNull(result)
    }

    @Test
    fun testNodeDescription() {
        val node = createMockNode(
            className = "android.widget.Button",
            text = "Click Me",
            id = "button_id",
            clickable = true
        )
        
        val description = node.describe()
        assertTrue("Description should contain class name", description.contains("Button"))
        // 注意：由于模拟对象的限制，可能无法完全测试所有功能
    }

    @Test
    fun testAttributeMatching() {
        // 这个测试主要验证方法调用不会抛出异常
        val node = createMockNode("android.widget.TextView", text = "Test")
        
        // 测试属性获取
        val text = node.text?.toString()
        assertEquals("Test", text)
    }

    @Test
    fun testResourceManagement() {
        val rootNode = createMockNode("android.widget.FrameLayout")
        
        // 测试安全操作
        rootNode.xpathUse("//TextView") { result ->
            assertNotNull(result)
            // 在这个块中使用结果
            result.exists()
        }
        // 资源应该在块结束后自动回收
    }

    @Test
    fun testErrorHandling() {
        val rootNode = createMockNode("android.widget.FrameLayout")
        
        // 测试空结果处理
        val emptyResult = rootNode.xpath("//NonExistentElement")
        assertFalse(emptyResult.exists())
        assertNull(emptyResult.first())
        assertTrue(emptyResult.texts().isEmpty())
    }

    @Test
    fun testBackwardCompatibility() {
        val rootNode = createMockNode("android.widget.FrameLayout")
        
        // 测试向后兼容的方法
        val node = rootNode.findNodeByXPath("//TextView")
        val nodes = rootNode.findNodesByXPath("//TextView")
        val exists = rootNode.existsByXPath("//TextView")
        val clicked = rootNode.clickByXPath("//Button")
        
        // 这些方法应该能正常调用而不抛出异常
        assertNotNull("Backward compatibility methods should work", nodes)
    }

    @Test
    fun testComplexXPathExpressions() {
        val rootNode = createMockNode("android.widget.FrameLayout")
        
        // 测试复杂的 XPath 表达式
        val complexQueries = listOf(
            "//TextView[@text='Hello' and @clickable='true']",
            "//Button[position()=1]",
            "//ViewGroup[last()]",
            "//TextView[@text~='partial']",
            "//Button[@text^='Start']",
            "//TextView[@text$='End']",
            "//EditText[@text?='\\d+']"
        )
        
        complexQueries.forEach { query ->
            try {
                val result = rootNode.xpath(query)
                assertNotNull("Query should not return null: $query", result)
            } catch (e: Exception) {
                fail("Query should not throw exception: $query - ${e.message}")
            }
        }
    }

    @Test
    fun testPerformanceOptimizations() {
        val rootNode = createMockNode("android.widget.FrameLayout", childCount = 100)
        
        // 创建大量子节点来测试性能
        for (i in 0 until 100) {
            val child = createMockNode("android.widget.TextView", text = "Item $i")
            `when`(rootNode.getChild(i)).thenReturn(child)
            `when`(child.parent).thenReturn(rootNode)
        }
        
        // 测试批量查询
        val startTime = System.currentTimeMillis()
        val result = rootNode.xpath("//TextView")
        val endTime = System.currentTimeMillis()
        
        // 验证查询能够完成（性能测试在单元测试中可能不太准确）
        assertNotNull(result)
        assertTrue("Query should complete in reasonable time", endTime - startTime < 1000)
    }
}
