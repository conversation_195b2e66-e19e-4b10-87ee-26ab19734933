package com.coffee.chatbot.service

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.Service
import android.content.Context
import android.content.Intent
import android.graphics.PixelFormat
import android.os.Build
import android.os.Handler
import android.os.Looper
import android.os.IBinder
import android.provider.Settings
import android.util.Log
import android.view.Gravity
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.widget.ImageButton
import android.widget.ScrollView
import android.widget.TextView
import android.widget.Toast
import androidx.core.app.NotificationCompat
import com.coffee.chatbot.R
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch
import kotlinx.coroutines.delay
import com.coffee.chatbot.model.ChatMessage

class FloatingWindowService : Service() {
    companion object {
        private const val TAG = "FloatingWindowService"
        private const val NOTIFICATION_ID = 1
        private const val CHANNEL_ID = "FloatingWindowServiceChannel"
        
        private var instance: FloatingWindowService? = null
        
        fun getInstance(): FloatingWindowService? {
            return instance
        }
    }
    
    private lateinit var windowManager: WindowManager
    private lateinit var floatingView: View
    private lateinit var logView: View
    private lateinit var logScrollView: ScrollView
    private lateinit var logTextView: TextView
    private val coroutineScope = CoroutineScope(Dispatchers.Main + SupervisorJob())
    private val handler = Handler(Looper.getMainLooper())
    
    private var isAutomationRunning = false
    private var isMonitoringMode = false
    
    override fun onCreate() {
        super.onCreate()
        instance = this
        Log.d(TAG, "FloatingWindowService onCreate")
        createNotificationChannel()
        startForeground(NOTIFICATION_ID, createNotification())
    }
    
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        Log.d(TAG, "FloatingWindowService onStartCommand: ${intent?.action}")
        
        if (intent?.action == "STOP_SERVICE") {
            stopSelf()
            return START_NOT_STICKY
        }
        
        if (!::floatingView.isInitialized) {
            createFloatingWindow()
            createLogWindow()
        }
        
        return START_STICKY
    }
    
    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                "Floating Window Service",
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "Shows a persistent notification while the floating window is active"
            }
            
            val notificationManager = getSystemService(NotificationManager::class.java)
            notificationManager.createNotificationChannel(channel)
        }
    }
    
    private fun createNotification(): Notification {
        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("Chatbot Active")
            .setContentText("The floating button is active")
            .setSmallIcon(R.drawable.ic_launcher_foreground)
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .build()
    }
    
    private fun createFloatingWindow() {
        Log.d(TAG, "Creating floating window")
        windowManager = getSystemService(Context.WINDOW_SERVICE) as WindowManager
        
        val layoutInflater = LayoutInflater.from(this)
        floatingView = layoutInflater.inflate(R.layout.floating_button_layout, null)
        
        val params = WindowManager.LayoutParams(
            WindowManager.LayoutParams.WRAP_CONTENT,
            WindowManager.LayoutParams.WRAP_CONTENT,
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
            } else {
                WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
            },
            WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE,
            PixelFormat.TRANSLUCENT
        ).apply {
            // 使用绝对坐标定位，允许在屏幕任意位置拖动
            gravity = Gravity.TOP or Gravity.START
            // 初始位置设为屏幕右侧
            val metrics = resources.displayMetrics
            val buttonSize = (60 * metrics.density).toInt()
            x = metrics.widthPixels - buttonSize - 20
            y = metrics.heightPixels / 3
        }
        
        setupDragMovement(params)
        
        val floatingButton = floatingView.findViewById<ImageButton>(R.id.floating_button)
        floatingButton.setOnClickListener {
            toggleAutomation()
        }
        
        try {
            windowManager.addView(floatingView, params)
            Log.d(TAG, "Floating view added successfully")
        } catch (e: Exception) {
            Log.e(TAG, "Error adding floating view", e)
            stopSelf() // 如果无法显示悬浮窗，停止服务
        }
    }
    
    private fun createLogWindow() {
        val layoutInflater = LayoutInflater.from(this)
        logView = layoutInflater.inflate(R.layout.log_display_layout, null)
        logScrollView = logView.findViewById(R.id.log_scroll_view)
        logTextView = logView.findViewById(R.id.log_text_view)

        val metrics = resources.displayMetrics
        val params = WindowManager.LayoutParams(
            metrics.widthPixels / 2, // 屏幕宽度的一半
            metrics.heightPixels / 2, // 屏幕高度的一半
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
            } else {
                WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
            },
            WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE,
            PixelFormat.TRANSLUCENT
        ).apply {
            gravity = Gravity.BOTTOM or Gravity.START
            x = 0
            y = 0
        }

        try {
            windowManager.addView(logView, params)
            logView.visibility = View.GONE // Initially hidden
            Log.d(TAG, "Log view added successfully")
        } catch (e: Exception) {
            Log.e(TAG, "Error adding log view", e)
        }
    }

    private fun showLogWindow() {
        if (::logView.isInitialized) {
            logView.visibility = View.VISIBLE
            logTextView.text = "" // Clear previous logs
        }
    }

    private fun hideLogWindow() {
        if (::logView.isInitialized) {
            logView.visibility = View.GONE
        }
    }

    fun appendLog(message: String) {
        handler.post {
            if (::logTextView.isInitialized) {
                logTextView.append("$message\n")
                // 自动滚动到底部
                logScrollView.post {
                    logScrollView.fullScroll(View.FOCUS_DOWN)
                }
            }
        }
    }
    
    private fun setupDragMovement(params: WindowManager.LayoutParams) {
        var initialX = 0
        var initialY = 0
        var initialTouchX = 0f
        var initialTouchY = 0f
        var isTap = true
        val tapThreshold = 10

        val dragView = floatingView.findViewById<ImageButton>(R.id.floating_button)

        dragView.setOnTouchListener { view, event ->
            when (event.action) {
                MotionEvent.ACTION_DOWN -> {
                    initialX = params.x
                    initialY = params.y
                    initialTouchX = event.rawX
                    initialTouchY = event.rawY
                    isTap = true
                    true
                }
                MotionEvent.ACTION_MOVE -> {
                    val deltaX = event.rawX - initialTouchX
                    val deltaY = event.rawY - initialTouchY
                    // 判断是否为拖动而非点击
                    if (isTap && (Math.abs(deltaX) > tapThreshold ||
                        Math.abs(deltaY) > tapThreshold)) {
                        isTap = false
                    }

                    if (!isTap) {
                        // 计算新位置
                        params.x = initialX + deltaX.toInt()
                        params.y = initialY + deltaY.toInt()

                        // 限制在屏幕范围内
                        checkBoundaries(params)

                        // 更新悬浮窗位置
                        try {
                            windowManager.updateViewLayout(floatingView, params)
                        } catch (e: IllegalArgumentException) {
                            Log.e(TAG, "Error updating view layout", e)
                        }
                    }
                    true
                }
                MotionEvent.ACTION_UP -> {
                    // 只有是点击才处理点击事件
                    if (isTap) {
                        view.performClick()
                    }
                    true
                }
                else -> false
            }
        }
    }
    
    // 检查并调整窗口位置，确保在屏幕范围内
    private fun checkBoundaries(params: WindowManager.LayoutParams) {
        val metrics = resources.displayMetrics
        val screenWidth = metrics.widthPixels
        val screenHeight = metrics.heightPixels
        
        // 获取浮动按钮尺寸
        val buttonSize = (60 * resources.displayMetrics.density).toInt()
        
        // 限制水平位置
        if (params.x < 0) {
            params.x = 0
        } else if (params.x > screenWidth - buttonSize) {
            params.x = screenWidth - buttonSize
        }
        
        // 限制垂直位置
        if (params.y < 0) {
            params.y = 0
        } else if (params.y > screenHeight - buttonSize) {
            params.y = screenHeight - buttonSize
        }
    }
    
    private fun toggleAutomation() {
        isAutomationRunning = !isAutomationRunning
        
        val button = floatingView.findViewById<ImageButton>(R.id.floating_button)
        
        if (isAutomationRunning) {
            button.setImageResource(android.R.drawable.ic_media_pause)
            showLogWindow()
            handleChatAutomation()
        } else {
            button.setImageResource(android.R.drawable.ic_media_play)
            hideLogWindow()
            stopAutomation()
        }
    }
    
    private fun handleChatAutomation() {
        coroutineScope.launch {
            // 首先检查无障碍服务是否在系统中启用
            if (!ChatbotAccessibilityService.isAccessibilityServiceEnabled(this@FloatingWindowService)) {
                Log.w(TAG, "无障碍服务未在系统设置中启用")
                Toast.makeText(this@FloatingWindowService, 
                    "请先在系统设置中启用无障碍服务", 
                    Toast.LENGTH_LONG).show()
                
                // 打开无障碍设置页面
                val intent = Intent(Settings.ACTION_ACCESSIBILITY_SETTINGS)
                intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
                startActivity(intent)
                
                // 重置按钮状态
                isAutomationRunning = false
                val button = floatingView.findViewById<ImageButton>(R.id.floating_button)
                button.setImageResource(android.R.drawable.ic_media_play)
                return@launch
            }
            
            // 尝试强制启动无障碍服务
            ChatbotAccessibilityService.forceStart(this@FloatingWindowService)
            
            // 等待一段时间，让系统有机会创建服务实例
            delay(500)
            
            var accessibilityService: ChatbotAccessibilityService? = null
            var attempts = 0
            val maxAttempts = 10 // 最多尝试10次 (10 * 200ms = 2 秒)

            while (attempts < maxAttempts) {
                accessibilityService = ChatbotAccessibilityService.getInstance()
                if (accessibilityService != null) {
                    break
                }
                attempts++
                delay(200) // 等待200毫秒
            }

            if (accessibilityService == null) {
                Log.w(TAG, "无法获取AccessibilityService实例，尝试 $maxAttempts 次后失败")
                Toast.makeText(this@FloatingWindowService, 
                    "无法获取辅助功能服务，请重启应用或重新开启无障碍服务", 
                    Toast.LENGTH_LONG).show()
                // 重置按钮状态
                isAutomationRunning = false
                val button = floatingView.findViewById<ImageButton>(R.id.floating_button)
                button.setImageResource(android.R.drawable.ic_media_play)
                return@launch
            }

            // 获取到服务实例后继续执行
            Log.d(TAG, "成功获取AccessibilityService实例")

            // 尝试处理客户接待列表页
            val isListHandled = accessibilityService.handleCustomerServiceList()
            if (isListHandled) {
                // 已在列表页找到并点击了未读消息，状态会更新为提取中
                isMonitoringMode = false
                Toast.makeText(this@FloatingWindowService, "已检测到新消息，正在进入会话", Toast.LENGTH_SHORT).show()
            } else {
                // 未找到未读消息，直接开始提取当前页面聊天内容
                isMonitoringMode = false
                accessibilityService.startChatExtraction()
                Toast.makeText(this@FloatingWindowService, "开始提取聊天内容", Toast.LENGTH_SHORT).show()
            }
        }
    }
    
    private fun stopAutomation() {
        val accessibilityService = ChatbotAccessibilityService.getInstance()
        if (accessibilityService != null) {
            if (isMonitoringMode) {
                accessibilityService.stopListeningForNewMessages()
                isMonitoringMode = false
                Toast.makeText(this, "已停止监听模式", Toast.LENGTH_SHORT).show()
            } else {
                accessibilityService.stopChatExtraction()
                Toast.makeText(this, "已停止提取聊天内容", Toast.LENGTH_SHORT).show()
            }
        }
    }
    
    private fun updateButtonForMonitoring() {
        val button = floatingView.findViewById<ImageButton>(R.id.floating_button)
        
        // 监听模式下显示不同图标，例如搜索或刷新图标
        button.setImageResource(android.R.drawable.ic_menu_search)
        
        // 可选：闪烁动画以表示正在监听
        button.alpha = 0.7f
        button.animate().alpha(1.0f).setDuration(1000).withEndAction {
            if (isMonitoringMode && isAutomationRunning) {
                button.alpha = 0.7f
                button.animate().alpha(1.0f).setDuration(1000).withEndAction {
                    if (isMonitoringMode && isAutomationRunning) {
                        updateButtonForMonitoring() // 递归调用以创建持续闪烁效果
                    }
                }.start()
            } else {
                button.alpha = 1.0f
            }
        }.start()
    }
    
    override fun onBind(intent: Intent?): IBinder? = null
    
    override fun onDestroy() {
        Log.d(TAG, "FloatingWindowService onDestroy")
        super.onDestroy()
        if (::floatingView.isInitialized && floatingView.isAttachedToWindow) {
            windowManager.removeView(floatingView)
        }
        if (::logView.isInitialized && logView.isAttachedToWindow) {
            windowManager.removeView(logView)
        }
        instance = null
    }
}
