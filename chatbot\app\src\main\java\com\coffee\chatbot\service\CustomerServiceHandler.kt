package com.coffee.chatbot.service

import android.accessibilityservice.AccessibilityService
import android.accessibilityservice.GestureDescription
import android.content.Context
import android.graphics.*
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.Gravity
import android.view.View
import android.view.WindowManager
import android.view.accessibility.AccessibilityNodeInfo
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.util.concurrent.atomic.AtomicReference

/**
 * 客服接待页面处理器 - 使用优化后的 XPath API
 * 保持原有接口兼容性，内部使用新的优雅 XPath 功能
 */
class CustomerServiceHandler(
    private val context: Context,
    private val openAIService: OpenAIService
) {

    companion object {
        private const val TAG = "CustomerServiceHandler"
        private const val TARGET_PACKAGE = "com.xingin.eva"
        private const val CLICK_MARKER_DURATION = 2000L

        // 使用优化后的 XPath 表达式
        private const val CONVERSATION_LIST_XPATH = "//TextView[@text='当前会话']/../../ViewGroup[last()]/ScrollView[0]/ViewGroup[0]"
        // 新的未读消息标识 XPath (相对于会话项)
        private const val UNREAD_INDICATOR_XPATH = "/ViewGroup[1]/ViewGroup[0]/ViewGroup[0]/ViewGroup[0]/ViewGroup[1]/TextView[0]"

        // 会话项内部元素的 XPath
        private const val NICKNAME_XPATH = "/ViewGroup[1]/ViewGroup[0]/ViewGroup[0]/ViewGroup[1]/TextView[0]"
        private const val TIME_XPATH = "/ViewGroup[1]/ViewGroup[0]/ViewGroup[0]/ViewGroup[1]/TextView[1]"
        private const val LAST_MESSAGE_XPATH = "/ViewGroup[1]/ViewGroup[0]/ViewGroup[0]/ViewGroup[1]/TextView[2]"
    }

    private var accessibilityService: AccessibilityService? = null
    private val handler = Handler(Looper.getMainLooper())
    private var showClickMarker = true
    private var markerView: View? = null
    private val windowManager by lazy { context.getSystemService(Context.WINDOW_SERVICE) as WindowManager }

    private var lastChatSummary: String? = null

    // 防重复发送机制
    private var lastSentMessage: String? = null
    private var lastSentTime: Long = 0
    private val sendCooldownMs = 3000 // 3秒冷却时间

    // 实时节点推送机制 - 参考WebViewService的设计
    private val currentRootNode = AtomicReference<AccessibilityNodeInfo?>(null)

    fun setAccessibilityService(service: AccessibilityService) {
        this.accessibilityService = service
        Log.d(TAG, "✅ AccessibilityService 已设置")
    }

    /**
     * 更新当前根节点 - 实时推送机制
     * 参考WebViewService的设计，由AccessibilityService主动推送最新节点
     * @param rootNode 最新的根节点信息
     */
    fun updateRootNode(rootNode: AccessibilityNodeInfo?) {
        // Create a copy of the node to prevent issues with recycling
        val oldNode = currentRootNode.getAndSet(rootNode?.let { AccessibilityNodeInfo(it) })
        oldNode?.recycle()
        Log.d(TAG, "🔄 节点已更新: ${if (rootNode != null) "有效节点" else "空节点"}")
    }

    /**
     * 获取当前存储的根节点
     * @return 当前存储的根节点，可能为null
     */
    private fun getCurrentRootNode(): AccessibilityNodeInfo? {
        return currentRootNode.get()
    }

    /**
     * 主要入口方法：检查并点击有未读消息的会话
     */
    fun checkAndEnterNewMessage(): Boolean {
        Log.d(TAG, "🚀 === 开始检查未读消息 ===")

        val service = accessibilityService
        if (service == null) {
            Log.e(TAG, "❌ AccessibilityService 未设置")
            return false
        }

        val rootNode = service.rootInActiveWindow
        if (rootNode == null) {
            Log.w(TAG, "❌ 无法获取根节点")
            return false
        }

        try {
            // 验证是否在目标应用
            if (!isTargetApp(rootNode)) {
                Log.w(TAG, "❌ 不在目标应用中")
                return false
            }

            // 检查是否在客服接待页面
            if (!isInCustomerServicePage(rootNode)) {
                Log.w(TAG, "❌ 不在客服接待页面")
                return false
            }

            Log.d(TAG, "✅ 确认在客服接待页面")

            // 查找并点击有未读消息的会话
            val result = findAndClickUnreadConversation(rootNode)
            Log.d(TAG, "🚀 === 检查未读消息完成，结果: $result ===")
            return result

        } catch (e: Exception) {
            Log.e(TAG, "检查未读消息时发生异常", e)
            return false
        } finally {
            rootNode.recycle()
        }
    }

    /**
     * 验证是否在目标应用
     */
    private fun isTargetApp(rootNode: AccessibilityNodeInfo): Boolean {
        val packageName = rootNode.packageName?.toString()
        Log.d(TAG, "当前应用包名: $packageName")
        return packageName == TARGET_PACKAGE
    }

    /**
     * 检查是否在客服接待页面 - 使用优化后的 XPath API
     */
    private fun isInCustomerServicePage(rootNode: AccessibilityNodeInfo): Boolean {
        // 使用新的便捷方法查找"客服接待"文本
        val customerServiceNodes = rootNode.findAllByText("客服接待", exact = false)
        Log.d(TAG, "找到 ${customerServiceNodes.size} 个'客服接待'文本节点")

        val exists = customerServiceNodes.any { it.isVisibleToUser } // 检查是否有任何一个可见
        customerServiceNodes.recycle() // 自动资源管理
        return exists
    }

    /**
     * 公开方法：检查是否在客服接待页面
     * @return 是否在客服接待页面
     */
    fun isInCustomerServicePage(): Boolean {
        val service = accessibilityService
        if (service == null) {
            Log.e(TAG, "❌ AccessibilityService 未设置")
            return false
        }

        val rootNode = service.rootInActiveWindow
        if (rootNode == null) {
            Log.w(TAG, "❌ 无法获取根节点")
            return false
        }

        try {
            return isInCustomerServicePage(rootNode)
        } catch (e: Exception) {
            Log.e(TAG, "检查客服接待页面时发生异常", e)
            return false
        } finally {
            rootNode.recycle()
        }
    }

    /**
     * 查找并点击有未读消息的会话 - 使用优化后的 XPath API
     */
    private fun findAndClickUnreadConversation(rootNode: AccessibilityNodeInfo): Boolean {
        return rootNode.xpathUse(CONVERSATION_LIST_XPATH) { containerResult ->
            val conversationContainer = containerResult.first()
            if (conversationContainer == null) {
                Log.w(TAG, "❌ 未找到会话列表容器")
                return@xpathUse false
            }

            Log.d(TAG, "✅ 找到会话列表容器: childCount=${conversationContainer.childCount}")

            // 2. 然后，继续执行原有的策略，查找并点击一个未读会话
            findAndClickUnreadConversationOptimized(conversationContainer)
        }
    }

    /**
     * 优化的未读会话查找和点击逻辑
     */
    private fun findAndClickUnreadConversationOptimized(container: AccessibilityNodeInfo): Boolean {
        val childCount = container.childCount

        for (i in 0 until childCount) {
            val conversationItem = container.getChild(i) ?: continue

            // 使用新的XPath检查未读标识
            val unreadResult = conversationItem.xpath(UNREAD_INDICATOR_XPATH)

            if (unreadResult.exists()) {
                unreadResult.recycle() // The result is not needed anymore

                // 使用找到的会话项调用点击函数
                // clickFirstUnreadConversation 会回收 conversationItem
                return clickFirstUnreadConversation(XPathResult(listOf(conversationItem)))
            }

            unreadResult.recycle()
            conversationItem.recycle()
        }

        Log.w(TAG, "❌ 新的 XPath 策略未找到任何未读会话")
        return false
    }

    /**
     * 点击第一个未读会话 - 使用优化的方法
     */
    private fun clickFirstUnreadConversation(conversations: XPathResult): Boolean {
        // 点击第一个对用户可见的会话
        val firstConversation = conversations.firstOrNull { it.isVisibleToUser } ?: return false

        // 打印会话信息用于调试
        printConversationInfo(firstConversation)

        // 使用多种策略查找最佳点击目标
        val success = performSmartClick(firstConversation)

        // 验证点击是否成功（检查页面是否发生变化）
        if (success) {
            val verified = verifyClickSuccess()
            if (verified) {
                Log.d(TAG, "✅ 点击成功，页面已跳转")
            } else {
                Log.w(TAG, "⚠️ 点击执行了但页面可能未跳转")
            }
        }

        conversations.recycle() // 清理资源
        return success
    }

    /**
     * 智能点击策略 - 尝试多种方法找到最佳点击位置
     */
    private fun performSmartClick(conversation: AccessibilityNodeInfo): Boolean {
        // 策略1: 查找会话中间区域的可点击元素（通常是主要内容区域）
        val centerClickable = findCenterClickableArea(conversation)
        if (centerClickable != null) {
            val success = performClickOnNode(centerClickable)
            centerClickable.recycle()
            if (success) return true
        }

        // 策略2: 查找昵称区域（通常可点击）
        val nicknameArea = conversation.xpath(NICKNAME_XPATH).first()
        if (nicknameArea != null) {
            val success = performClickOnNode(nicknameArea)
            if (success) return true
        }

        // 策略3: 查找任何可点击的子元素
        val anyClickable = conversation.xpath(".//[@clickable='true']").first()
        if (anyClickable != null) {
            val success = performClickOnNode(anyClickable)
            if (success) return true
        }

        // 策略4: 直接点击会话容器
        return performClickOnNode(conversation)
    }

    /**
     * 查找会话中心区域的可点击元素
     */
    private fun findCenterClickableArea(conversation: AccessibilityNodeInfo): AccessibilityNodeInfo? {
        val conversationBounds = Rect()
        conversation.getBoundsInScreen(conversationBounds)

        val centerY = conversationBounds.centerY()
        val tolerance = conversationBounds.height() / 4 // 允许25%的误差

        // 查找Y坐标接近中心的可点击元素
        val clickableElements = conversation.xpath(".//[@clickable='true']")

        for (element in clickableElements) {
            val elementBounds = Rect()
            element.getBoundsInScreen(elementBounds)

            if (Math.abs(elementBounds.centerY() - centerY) <= tolerance) {
                Log.d(TAG, "找到中心区域可点击元素: ${element.describe()}")
                clickableElements.recycle()
                return element
            }
        }

        clickableElements.recycle()
        return null
    }

    /**
     * 打印会话信息用于调试 - 使用优化的方法
     */
    private fun printConversationInfo(conversation: AccessibilityNodeInfo) {
        // 使用您提供的精确 XPath 路径进行链式查询
        val nicknameResult = conversation.xpath(NICKNAME_XPATH)
        val timeResult = conversation.xpath(TIME_XPATH)
        val lastMessageResult = conversation.xpath(LAST_MESSAGE_XPATH)

        val nickname = nicknameResult.text()
        val time = timeResult.text()
        val lastMessage = lastMessageResult.text()

        Log.d(TAG, "会话信息:")
        Log.d(TAG, "  昵称: '${nickname ?: "未找到"}'")
        Log.d(TAG, "  时间: '${time ?: "未找到"}'")
        Log.d(TAG, "  最后消息: '${lastMessage ?: "未找到"}'")

        // 检查是否有未读标识
        // 检查是否有未读标识
        val unreadResult = conversation.xpath(UNREAD_INDICATOR_XPATH)
        val hasUnread = unreadResult.exists()
        Log.d(TAG, "  未读状态: $hasUnread")
        unreadResult.recycle()

        // 回收所有查询结果
        nicknameResult.recycle()
        timeResult.recycle()
        lastMessageResult.recycle()
    }

    /**
     * 在节点上执行点击操作 - 增强版本
     */
    private fun performClickOnNode(node: AccessibilityNodeInfo): Boolean {
        val bounds = Rect()
        node.getBoundsInScreen(bounds)

        if (bounds.isEmpty) {
            Log.e(TAG, "❌ 节点边界为空，无法点击")
            return false
        }

        // 策略 1: 直接使用 performAction(ACTION_CLICK)
        if (node.isClickable && node.performAction(AccessibilityNodeInfo.ACTION_CLICK)) {
            showClickMarkerIfEnabled(bounds.centerX(), bounds.centerY())
            Thread.sleep(300) // 给系统反应时间
            return true
        }

        // 策略 2: 查找可点击的父节点
        val clickableParent = findClickableParent(node)
        if (clickableParent != null && clickableParent != node) {
            val success = clickableParent.performAction(AccessibilityNodeInfo.ACTION_CLICK)
            if (success) {
                val parentBounds = Rect()
                clickableParent.getBoundsInScreen(parentBounds)
                showClickMarkerIfEnabled(parentBounds.centerX(), parentBounds.centerY())
                clickableParent.recycle()
                Thread.sleep(300)
                return true
            }
            clickableParent.recycle()
        }

        // 策略 3: 尝试多个位置的手势点击
        Log.w(TAG, "⚠️ ACTION_CLICK 失败，尝试手势点击")
        return performMultiPositionGestureClick(bounds)
    }

    /**
     * 查找可点击的父节点
     */
    private fun findClickableParent(node: AccessibilityNodeInfo): AccessibilityNodeInfo? {
        var current: AccessibilityNodeInfo? = node.parent
        var depth = 0
        val maxDepth = 5 // 限制搜索深度

        while (current != null && depth < maxDepth) {
            if (current.isClickable) {
                return current
            }
            val parent = current.parent
            current.recycle()
            current = parent
            depth++
        }

        current?.recycle()
        return null
    }

    /**
     * 尝试多个位置的手势点击
     */
    private fun performMultiPositionGestureClick(bounds: Rect): Boolean {
        val positions = listOf(
            // 中心位置
            Pair(bounds.centerX(), bounds.centerY()),
            // 稍微偏左的位置（避开可能的按钮）
            Pair(bounds.left + bounds.width() / 3, bounds.centerY()),
            // 稍微偏右的位置
            Pair(bounds.right - bounds.width() / 3, bounds.centerY()),
            // 稍微偏上的位置
            Pair(bounds.centerX(), bounds.top + bounds.height() / 3)
        )

        for ((index, position) in positions.withIndex()) {
            val (x, y) = position
            Log.d(TAG, "🎯 尝试位置 ${index + 1}: ($x, $y)")

            showClickMarkerIfEnabled(x, y)

            if (performEnhancedGestureClick(x, y)) {
                Log.d(TAG, "✅ 位置 ${index + 1} 增强手势点击成功")
                Thread.sleep(500) // 给更多时间让界面响应
                return true
            }

            Thread.sleep(300) // 增加延时再尝试下一个位置
        }

        Log.e(TAG, "❌ 所有位置的手势点击都失败了")
        return false
    }

    /**
     * 显示点击标记（如果启用）
     */
    private fun showClickMarkerIfEnabled(x: Int, y: Int) {
        if (showClickMarker) {
            showClickMarker(x, y)
        }
    }

    /**
     * 执行手势点击 - 增强版本
     */
    private fun performGestureClick(x: Int, y: Int): Boolean {
        val service = accessibilityService
        if (service == null) {
            Log.e(TAG, "❌ AccessibilityService 为空")
            return false
        }

        // 检查手势权限 (Android N及以上版本)
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.N) {
            try {
                // 尝试创建一个简单的手势来测试权限
                val testPath = Path().apply { moveTo(0f, 0f) }
                val testGesture = GestureDescription.Builder()
                    .addStroke(GestureDescription.StrokeDescription(testPath, 0, 1))
                    .build()

                // 如果能创建手势描述，说明有基本权限
                Log.d(TAG, "✅ 手势权限检查通过")
            } catch (e: Exception) {
                Log.e(TAG, "❌ AccessibilityService 手势权限检查失败: ${e.message}")
                Log.e(TAG, "❌ 请检查 accessibility_service_config.xml 中是否包含 android:canPerformGestures=\"true\"")
                return false
            }
        }

        try {
            val path = Path().apply {
                moveTo(x.toFloat(), y.toFloat())
            }

            val gestureBuilder = GestureDescription.Builder()
            // 使用更短的点击持续时间，模拟快速点击
            val strokeDescription = GestureDescription.StrokeDescription(path, 0, 100)
            gestureBuilder.addStroke(strokeDescription)

            val gesture = gestureBuilder.build()

            var gestureCompleted = false
            var gestureCancelled = false

            val success = service.dispatchGesture(gesture, object : AccessibilityService.GestureResultCallback() {
                override fun onCompleted(gestureDescription: GestureDescription?) {
                    Log.d(TAG, "✅ 手势点击完成 at ($x, $y)")
                    gestureCompleted = true
                }

                override fun onCancelled(gestureDescription: GestureDescription?) {
                    Log.w(TAG, "❌ 手势点击被取消 at ($x, $y)")
                    gestureCancelled = true
                }
            }, null)

            if (!success) {
                Log.e(TAG, "❌ 手势分发失败 at ($x, $y)")
                return false
            }

            // 等待手势完成，最多等待1秒
            val startTime = System.currentTimeMillis()
            while (!gestureCompleted && !gestureCancelled &&
                   (System.currentTimeMillis() - startTime) < 1000) {
                Thread.sleep(50)
            }

            return gestureCompleted

        } catch (e: Exception) {
            Log.e(TAG, "❌ 执行手势点击时发生异常 at ($x, $y)", e)
            return false
        }
    }

    /**
     * 验证点击是否成功（通过检查页面变化）
     */
    private fun verifyClickSuccess(): Boolean {
        // 等待页面跳转
        Thread.sleep(1000)

        val service = accessibilityService ?: return false
        val rootNode = service.rootInActiveWindow ?: return false

        try {
            // 检查是否还在客服接待页面
            val stillInCustomerService = rootNode.findAllByText("客服接待", exact = false).exists()

            if (!stillInCustomerService) {
                Log.d(TAG, "✅ 页面已跳转，不再是客服接待页面")
                return true
            }

            // 检查是否出现了聊天界面的特征元素
            val hasChatFeatures = checkForChatFeatures(rootNode)
            if (hasChatFeatures) {
                Log.d(TAG, "✅ 检测到聊天界面特征")
                return true
            }

            Log.w(TAG, "⚠️ 仍在客服接待页面，点击可能未成功")
            return false

        } catch (e: Exception) {
            Log.e(TAG, "❌ 验证点击成功时发生异常", e)
            return false
        } finally {
            rootNode.recycle()
        }
    }

    /**
     * 检查聊天界面的特征元素
     */
    private fun checkForChatFeatures(rootNode: AccessibilityNodeInfo): Boolean {
        // 检查常见的聊天界面元素
        val chatFeatures = listOf(
            "//EditText", // 输入框
            "//[@text~='发送']", // 发送按钮
            "//[@desc~='发送']", // 发送按钮描述
            "//[@text~='输入']", // 输入提示
            "//[@desc~='消息']" // 消息相关描述
        )

        for (xpath in chatFeatures) {
            if (rootNode.xpath(xpath).exists()) {
                Log.d(TAG, "找到聊天特征: $xpath")
                return true
            }
        }

        return false
    }

    /**
     * 处理消息输入和发送 - 重构为激活EditText焦点并发送回车消息
     */
    fun handleMessageInput(rootNode: AccessibilityNodeInfo, message: String): Boolean {
        // 防重复发送检查
        val currentTime = System.currentTimeMillis()
        if (lastSentMessage == message && (currentTime - lastSentTime) < sendCooldownMs) {
            Log.w(TAG, "⚠️ 防重复发送: 相同消息在冷却时间内，跳过发送")
            Log.w(TAG, "⚠️ 上次发送: ${(currentTime - lastSentTime)}ms 前, 冷却时间: ${sendCooldownMs}ms")
            return true // 返回true避免重复尝试
        }
        // 1. 使用 XPath 查找输入框
        val inputField = rootNode.xpath("//EditText").first()
        if (inputField == null) {
            Log.w(TAG, "❌ XPath 未找到输入框: //EditText")
            return false
        }

        var success = false
        try {
            // 2. 直接输入文本，不再激活焦点
            Log.d(TAG, "📝 开始输入文本...")
            val bundle = android.os.Bundle().apply {
                putCharSequence(AccessibilityNodeInfo.ACTION_ARGUMENT_SET_TEXT_CHARSEQUENCE, message)
            }
            if (!inputField.performAction(AccessibilityNodeInfo.ACTION_SET_TEXT, bundle)) {
                Log.w(TAG, "❌ 输入文本 (ACTION_SET_TEXT) 失败")
                return false
            }
            Log.d(TAG, "✅ 文本已输入: $message")
            Thread.sleep(500) // 等待文本输入完成和UI响应

            // 4. 发送回车消息（多种策略）
            Log.d(TAG, "📤 发送回车消息...")
            success = sendEnterMessage()

            if (success) {
                // 验证已在 sendEnterMessage 内部完成
                Log.d(TAG, "✅ 消息发送成功")
                // 更新防重复发送状态
                lastSentMessage = message
                lastSentTime = System.currentTimeMillis()
                Log.d(TAG, "🔒 已记录发送状态，防止重复发送")
            } else {
                Log.e(TAG, "❌ 所有发送策略都失败了")
            }

        } catch (e: Exception) {
            Log.e(TAG, "处理消息输入时发生异常", e)
        } finally {
            inputField.recycle() // 回收节点
        }

        Log.d(TAG, if (success) "✅ 消息发送成功" else "❌ 消息发送失败")
        return success
    }

    /**
     * 发送回车消息的多种策略实现（6种策略）
     * @param inputField 原始输入框节点（仅用于参考，会重新获取）
     * @param rootNode 根节点，用于查找发送按钮和重新获取输入框
     * @return 是否发送成功
     */
    private fun sendEnterMessage(): Boolean {
        val service = accessibilityService ?: return false
        Log.d(TAG, "🔄 重新获取页面布局以确保状态最新...")
        val freshRootNode = service.rootInActiveWindow
        if (freshRootNode == null) {
            Log.w(TAG, "❌ 无法获取最新的根节点，操作中止")
            return false
        }

        // 关键优化：确保输入框有内容后，等待UI刷新，让发送按钮变为可见状态
        Log.d(TAG, "⏳ 等待UI刷新，确保发送按钮可见...")
        Thread.sleep(500) // 等待UI状态更新

        // 重新获取最新的节点树，确保发送按钮状态是最新的
        val updatedRootNode = service.rootInActiveWindow
        if (updatedRootNode == null) {
            Log.w(TAG, "❌ 无法获取更新后的根节点")
            freshRootNode.recycle()
            return false
        }

        try {
            // 从更新后的布局中查找输入框（使用最新的节点树）
            val currentInputField = updatedRootNode.xpath("//EditText").first()
            if (currentInputField == null) {
                Log.w(TAG, "❌ 在更新后的布局中未找到输入框，操作中止")
                return false
            }

            Log.d(TAG, "✅ 成功从更新后的布局中获取输入框节点")
            val result = sendEnterMessageWithInputField(currentInputField, updatedRootNode)
            currentInputField.recycle() // 回收在更新布局中找到的输入框节点
            return result

        } finally {
            freshRootNode.recycle() // 确保回收初始根节点
            updatedRootNode.recycle() // 确保回收更新后的根节点
        }
    }

    /**
     * 检查消息是否发送成功
     * 判断条件：点击发送后，等待1秒，检查EditText内的文本是否为"发消息..."
     * @param rootNode 根节点
     * @return 是否发送成功
     */
    private fun checkMessageSentSuccessfully(rootNode: AccessibilityNodeInfo): Boolean {
        Log.d(TAG, "🔍 检查消息发送是否成功...")

        // 等待1秒让发送操作完成
        Thread.sleep(1000)

        try {
            // 重新获取输入框
            val inputField = rootNode.xpath("//EditText").first()
            if (inputField == null) {
                Log.w(TAG, "❌ 无法获取输入框，无法判断发送状态")
                return false
            }

            val inputText = inputField.text?.toString() ?: ""
            Log.d(TAG, "🔍 当前输入框文本: '$inputText'")

            inputField.recycle()

            // 判断是否为默认提示文本
            val isSuccess = inputText == "发消息..."
            if (isSuccess) {
                Log.d(TAG, "✅ 消息发送成功确认: 输入框已重置为默认状态")
            } else {
                Log.w(TAG, "⚠️ 消息可能未发送成功: 输入框文本未重置")
            }

            return isSuccess

        } catch (e: Exception) {
            Log.e(TAG, "❌ 检查发送状态时发生异常", e)
            return false
        }
    }

    /**
     * 使用指定输入框节点执行发送策略
     * @param inputField 当前有效的输入框节点
     * @param rootNode 根节点
     * @return 是否发送成功
     */
    private fun sendEnterMessageWithInputField(inputField: AccessibilityNodeInfo, rootNode: AccessibilityNodeInfo): Boolean {
        // 关键优化：在策略1之前重新获取最新节点树，确保发送按钮状态是最新的
        Log.d(TAG, "🔄 策略1前：重新获取最新节点树以确保发送按钮可见...")
        val service = accessibilityService ?: return false
        val refreshedRootNode = service.rootInActiveWindow
        if (refreshedRootNode == null) {
            Log.w(TAG, "❌ 策略1: 无法获取刷新后的节点树")
            return false
        }

        // try {
        //     // 策略1: 通过查找包含“发送”文本且可点击的任何节点来点击发送按钮（核心策略）
        //     Log.d(TAG, "🎯 策略1: 尝试通过'发送'文本和可点击状态查找按钮")
        //     val sendButtonNode = refreshedRootNode.xpath("//*[@clickable='true' and .//android.widget.TextView[@text='发送']]").first()
        //     if (sendButtonNode != null) {
        //         Log.d(TAG, "✅ 策略1: 找到发送按钮: ${sendButtonNode.describe()}")
        //         val bounds = Rect()
        //         sendButtonNode.getBoundsInScreen(bounds)
        //         if (!bounds.isEmpty) {
        //             val clickSuccess = performEnhancedGestureClick(bounds.centerX(), bounds.centerY())
        //             sendButtonNode.recycle()
        //             if (clickSuccess) {
        //                 Log.d(TAG, "✅ 策略1执行完成: 手势点击")
        //                 // 使用刷新后的节点检查发送状态
        //                 val finalCheckNode = service.rootInActiveWindow
        //                 if (finalCheckNode != null) {
        //                     val success = checkMessageSentSuccessfully(finalCheckNode)
        //                     finalCheckNode.recycle()
        //                     if (success) {
        //                         Log.d(TAG, "✅ 策略1成功: 手势点击发送")
        //                         return true
        //                     } else {
        //                         Log.w(TAG, "⚠️ 策略1失败: 手势点击后消息未确认发送")
        //                     }
        //                 }
        //             }
        //         } else {
        //             Log.w(TAG, "⚠️ 策略1: 发送按钮边界为空，无法点击")
        //             sendButtonNode.recycle()
        //         }
        //     } else {
        //         Log.w(TAG, "⚠️ 策略1: 未找到包含'发送'文本的可点击节点")
        //     }

        // } finally {
        //     refreshedRootNode.recycle() // 确保回收刷新后的节点
        // }
        // Thread.sleep(300) // 策略间延时
        
        // 策略2: 多位置手势点击发送区域（优先策略）
        // Log.d(TAG, "🎯 策略2: 尝试多位置手势点击发送区域")
        // val inputBounds = android.graphics.Rect()
        // inputField.getBoundsInScreen(inputBounds)
        // if (performMultiPositionGestureClick(inputBounds)) {
        //     Log.d(TAG, "✅ 策略2执行完成: 多位置手势点击")
        //     if (checkMessageSentSuccessfully(rootNode)) {
        //         Log.d(TAG, "✅ 策略2成功: 多位置手势点击发送")
        //         return true
        //     } else {
        //         Log.w(TAG, "⚠️ 策略2失败: 手势点击未成功发送消息")
        //     }
        // }
        // Thread.sleep(300) // 策略间延时

        // // 策略3: 使用输入法的发送动作
        // Log.d(TAG, "🎯 策略3: 尝试输入法发送动作")
        // val imeBundle = android.os.Bundle().apply {
        //     putInt(AccessibilityNodeInfo.ACTION_ARGUMENT_MOVEMENT_GRANULARITY_INT,
        //            AccessibilityNodeInfo.MOVEMENT_GRANULARITY_LINE)
        // }
        // if (inputField.performAction(AccessibilityNodeInfo.ACTION_NEXT_AT_MOVEMENT_GRANULARITY, imeBundle)) {
        //     Log.d(TAG, "✅ 策略3执行完成: 输入法发送动作")
        //     if (checkMessageSentSuccessfully(rootNode)) {
        //         Log.d(TAG, "✅ 策略3成功: 输入法发送动作")
        //         return true
        //     } else {
        //         Log.w(TAG, "⚠️ 策略3失败: 输入法动作未成功发送消息")
        //     }
        // }
        // Thread.sleep(300) // 策略间延时

        // 策略4: 尝试使用 PASTE 动作触发发送（某些应用的特殊行为）
        // Log.d(TAG, "🎯 策略4: 尝试 PASTE 动作触发发送")
        // if (inputField.performAction(AccessibilityNodeInfo.ACTION_PASTE)) {
        //     Log.d(TAG, "✅ 策略4执行完成: PASTE 动作触发发送")
        //     if (checkMessageSentSuccessfully(rootNode)) {
        //         Log.d(TAG, "✅ 策略4成功: PASTE 动作触发发送")
        //         return true
        //     } else {
        //         Log.w(TAG, "⚠️ 策略4失败: PASTE动作未成功发送消息")
        //     }
        // }
        // Thread.sleep(300) // 策略间延时

        // 策略5: 模拟键盘回车键（使用手势）
        // Log.d(TAG, "🎯 策略5: 尝试模拟回车键手势")
        // if (simulateEnterKeyGesture()) {
        //     Log.d(TAG, "✅ 策略5执行完成: 回车键手势模拟")
        //     if (checkMessageSentSuccessfully(rootNode)) {
        //         Log.d(TAG, "✅ 策略5成功: 回车键手势模拟")
        //         return true
        //     } else {
        //         Log.w(TAG, "⚠️ 策略5失败: 回车键手势未成功发送消息")
        //     }
        // }
        // Thread.sleep(300) // 策略间延时

        // 策略6: 点击软键盘回车键发送
        // 有效
        // Log.d(TAG, "🎯 策略6: 尝试点击软键盘回车键")
        // if (clickSoftKeyboardEnterKey(rootNode)) {
        //     Log.d(TAG, "✅ 策略6执行完成: 软键盘回车键点击")
        //     if (checkMessageSentSuccessfully(rootNode)) {
        //         Log.d(TAG, "✅ 策略6成功: 软键盘回车键点击")
        //         return true
        //     } else {
        //         Log.w(TAG, "⚠️ 策略6失败: 软键盘回车键未成功发送消息")
        //     }
        // }
        // Thread.sleep(300) // 策略间延时

        // 策略7: 利用EditText父组件的可点击特性
        // Log.d(TAG, "🎯 策略7: 尝试点击EditText父组件发送区域")

        // val allViewGroups = inputField.xpath("/../ViewGroup")
        // val sendParentArea = allViewGroups.at(1)
        // if (sendParentArea != null) {
        //     // 打印节点信息,包含其中的文本及结构
        //     Log.d(TAG, "🔍 发送区域节点树:")
        //     sendParentArea.printTree()
        //     Log.d(TAG, "🔍 发送区域节点信息: ${sendParentArea.describe()}")
        //     // 打印其中包含的所有文本
        //     val allTexts = sendParentArea.xpath(".//*[@text]").texts()
        //     Log.d(TAG, "🔍 发送区域包含文本: $allTexts")
        //     val success = performSmartClick(sendParentArea)
        //     sendParentArea.recycle()
        //     if (success) {
        //         Log.d(TAG, "✅ 策略7执行完成: EditText父组件发送区域点击")
        //         if (checkMessageSentSuccessfully(rootNode)) {
        //             Log.d(TAG, "✅ 策略7成功: EditText父组件发送区域点击")
        //             return true
        //         } else {
        //             Log.w(TAG, "⚠️ 策略7失败: 父组件点击未成功发送消息")
        //         }
        //     }
        // }
        // Thread.sleep(300) // 策略间延时

        // // 策略8: 查找并智能点击发送按钮（备用方案）
        // Log.d(TAG, "🎯 策略8: 尝试查找发送按钮")
        // // 重新获取发送按钮节点，因为之前的操作可能改变了UI状态
        // val sendButton = rootNode.findSendButton()
        // if (sendButton != null) {
        //     val success = performSmartClick(sendButton)
        //     sendButton.recycle()
        //     if (success) {
        //         Log.d(TAG, "✅ 策略8执行完成: 发送按钮智能点击")
        //         if (checkMessageSentSuccessfully(rootNode)) {
        //             Log.d(TAG, "✅ 策略8成功: 发送按钮智能点击")
        //             return true
        //         } else {
        //             Log.w(TAG, "⚠️ 策略8失败: 发送按钮点击未成功发送消息")
        //         }
        //     }
        // }

        // // 策略9: 使用系统级输入命令作为最后手段（需要root权限或shell权限）
        // Log.d(TAG, "🎯 策略9: 尝试使用系统级输入命令")
        // val systemInputSuccess = performSystemInputTap(1020, 2253) // 使用已知有效的坐标
        // if (systemInputSuccess) {
        //     Log.d(TAG, "✅ 策略9执行完成: 系统级输入命令点击")
        //     if (checkMessageSentSuccessfully(rootNode)) {
        //         Log.d(TAG, "✅ 策略9成功: 系统级输入命令点击")
        //         return true
        //     } else {
        //         Log.w(TAG, "⚠️ 策略9失败: 系统级命令未成功发送消息")
        //     }
        // }

        // 策略10: 使用原生AccessibilityService API点击发送按钮（最后手段）
        Thread.sleep(5000)

        // 关键优化：使用实时推送的节点树，参考WebViewService的被动接收模式
        Log.d(TAG, "🔄 策略10: 使用实时推送的节点树...")
        val strategy10RootNode = getCurrentRootNode()
        // 打印节点树结构
        Log.d(TAG, "🌳 策略10节点树结构:")
        strategy10RootNode?.printTree(maxDepth = 3, tag = TAG)
        if (strategy10RootNode == null) {
            Log.w(TAG, "❌ 策略10: 当前没有可用的推送节点，尝试主动获取...")
            // 如果没有推送节点，作为备用方案主动获取一次
            val service = accessibilityService ?: return false
            val fallbackRootNode = service.rootInActiveWindow
            if (fallbackRootNode == null) {
                Log.w(TAG, "❌ 策略10: 备用获取也失败，无法执行")
                return false
            }
            // 更新推送节点并同步到WebViewService
            updateRootNode(fallbackRootNode)
            val webViewService = WebViewService.getInstance()
            webViewService?.updateRootNode(fallbackRootNode)
            Log.d(TAG, "✅ 策略10: 已更新推送节点并同步WebViewService")

            try {
                Log.d(TAG, "🎯 策略10: 使用备用节点执行原生API查找并点击发送按钮")
                val success10 = clickSendButtonWithNativeAPIUsingLatestNode(fallbackRootNode)
                if (success10) {
                    Log.d(TAG, "✅ 策略10执行完成: 原生API发送按钮点击")
                    // 使用最新节点检查发送状态
                    val finalCheckNode = accessibilityService?.rootInActiveWindow
                    if (finalCheckNode != null) {
                        val success = checkMessageSentSuccessfully(finalCheckNode)
                        finalCheckNode.recycle()
                        if (success) {
                            Log.d(TAG, "✅ 策略10成功: 原生API发送按钮点击")
                            return true
                        } else {
                            Log.w(TAG, "⚠️ 策略10失败: 原生API点击未成功发送消息")
                        }
                    }
                }
            } finally {
                fallbackRootNode.recycle()
            }
        } else {
            Log.d(TAG, "✅ 策略10: 使用实时推送的节点树")
            Log.d(TAG, "🎯 策略10: 使用原生API查找并点击发送按钮")
            val success10 = clickSendButtonWithNativeAPIUsingLatestNode(strategy10RootNode)
            if (success10) {
                Log.d(TAG, "✅ 策略10执行完成: 原生API发送按钮点击")
                // 使用最新节点检查发送状态
                val service = accessibilityService ?: return false
                val finalCheckNode = service.rootInActiveWindow
                if (finalCheckNode != null) {
                    val success = checkMessageSentSuccessfully(finalCheckNode)
                    finalCheckNode.recycle()
                    if (success) {
                        Log.d(TAG, "✅ 策略10成功: 原生API发送按钮点击")
                        return true
                    } else {
                        Log.w(TAG, "⚠️ 策略10失败: 原生API点击未成功发送消息")
                    }
                }
            }
        }

        Log.w(TAG, "❌ 所有发送策略都失败了")
        return false
    }

    /**
     * 点击软键盘回车键发送消息
     * 查找软键盘上的回车/发送键并点击
     * @param rootNode 根节点，用于查找软键盘
     * @return 是否点击成功
     */
    private fun clickSoftKeyboardEnterKey(rootNode: AccessibilityNodeInfo): Boolean {
        Log.d(TAG, "🔍 开始查找软键盘回车键...")

        // 策略1: 查找包含"发送"文本的软键盘按键
        val sendKeyByText = rootNode.xpath("//[@text='发送' and @clickable='true']").first()
        if (sendKeyByText != null) {
            val success = performClickOnNode(sendKeyByText)
            sendKeyByText.recycle()
            if (success) {
                Log.d(TAG, "✅ 软键盘'发送'键点击成功")
                return true
            }
        }

        // 策略2: 查找包含"回车"或"换行"的按键
        val enterKeyByText = rootNode.xpath("//[@text='回车' or @text='换行' and @clickable='true']").first()
        if (enterKeyByText != null) {
            val success = performClickOnNode(enterKeyByText)
            enterKeyByText.recycle()
            if (success) {
                Log.d(TAG, "✅ 软键盘'回车'键点击成功")
                return true
            }
        }

        // 策略3: 查找包含"Enter"的按键
        val enterKeyByEnglish = rootNode.xpath("//[@text='Enter' and @clickable='true']").first()
        if (enterKeyByEnglish != null) {
            val success = performClickOnNode(enterKeyByEnglish)
            enterKeyByEnglish.recycle()
            if (success) {
                Log.d(TAG, "✅ 软键盘'Enter'键点击成功")
                return true
            }
        }

        // 策略4: 通过内容描述查找回车键
        val enterKeyByDesc = rootNode.xpath("//[@desc~='回车' or @desc~='发送' or @desc~='Enter' and @clickable='true']").first()
        if (enterKeyByDesc != null) {
            val success = performClickOnNode(enterKeyByDesc)
            enterKeyByDesc.recycle()
            if (success) {
                Log.d(TAG, "✅ 软键盘回车键（通过描述）点击成功")
                return true
            }
        }

        // 策略5: 查找软键盘区域的特定位置回车键
        val keyboardEnterKey = findKeyboardEnterKeyByPosition(rootNode)
        if (keyboardEnterKey != null) {
            val success = performClickOnNode(keyboardEnterKey)
            keyboardEnterKey.recycle()
            if (success) {
                Log.d(TAG, "✅ 软键盘回车键（通过位置）点击成功")
                return true
            }
        }

        Log.w(TAG, "❌ 未找到软键盘回车键")
        return false
    }

    /**
     * 通过位置查找软键盘的回车键
     * 通常回车键位于软键盘的右下角区域
     */
    private fun findKeyboardEnterKeyByPosition(rootNode: AccessibilityNodeInfo): AccessibilityNodeInfo? {
        // 获取屏幕尺寸
        val displayMetrics = context.resources.displayMetrics
        val screenWidth = displayMetrics.widthPixels
        val screenHeight = displayMetrics.heightPixels

        // 软键盘通常在屏幕下半部分
        val keyboardTopY = screenHeight * 0.6 // 屏幕高度的60%以下
        val keyboardBottomY = screenHeight * 0.95 // 屏幕高度的95%以上

        // 回车键通常在右侧区域
        val enterKeyLeftX = screenWidth * 0.7 // 屏幕宽度的70%以右

        // 查找在这个区域内的可点击元素
        val clickableElements = rootNode.xpath("//[@clickable='true']")

        for (element in clickableElements) {
            val bounds = Rect()
            element.getBoundsInScreen(bounds)

            // 检查是否在预期的回车键区域
            if (bounds.centerY() >= keyboardTopY &&
                bounds.centerY() <= keyboardBottomY &&
                bounds.centerX() >= enterKeyLeftX) {

                Log.d(TAG, "找到可能的软键盘回车键: ${element.describe()} at (${bounds.centerX()}, ${bounds.centerY()})")
                clickableElements.recycle()
                return element
            }
        }

        clickableElements.recycle()
        return null
    }

    /**
     * 使用系统级输入命令执行点击（需要shell权限）
     * 这是最后的备用方案，模拟 adb shell input tap 命令
     * @param x X坐标
     * @param y Y坐标
     * @return 是否执行成功
     */
    private fun performSystemInputTap(x: Int, y: Int): Boolean {
        return try {
            Log.d(TAG, "🔍 尝试执行系统级点击命令: input tap $x $y")

            // 方法1: 尝试使用Runtime.exec执行input命令
            val process = Runtime.getRuntime().exec(arrayOf("input", "tap", x.toString(), y.toString()))
            val exitCode = process.waitFor()

            if (exitCode == 0) {
                Log.d(TAG, "✅ 系统级input命令执行成功")
                return true
            } else {
                Log.w(TAG, "⚠️ 系统级input命令返回错误码: $exitCode")
            }

            // 方法2: 尝试使用su权限执行（如果设备已root）
            try {
                val suProcess = Runtime.getRuntime().exec(arrayOf("su", "-c", "input tap $x $y"))
                val suExitCode = suProcess.waitFor()

                if (suExitCode == 0) {
                    Log.d(TAG, "✅ 使用su权限的input命令执行成功")
                    return true
                } else {
                    Log.w(TAG, "⚠️ su权限input命令返回错误码: $suExitCode")
                }
            } catch (e: Exception) {
                Log.d(TAG, "ℹ️ 设备未root或无su权限: ${e.message}")
            }

            false
        } catch (e: Exception) {
            Log.e(TAG, "❌ 执行系统级input命令失败", e)
            false
        }
    }

    /**
     * 使用原生AccessibilityService API点击发送按钮
     * 策略：遍历所有节点，使用原生API查找和点击
     * @param rootNode 根节点
     * @return 是否点击成功
     */
    private fun clickSendButtonWithNativeAPI(): Boolean {
        Log.d(TAG, "🔍 使用原生API查找发送按钮...")
        val service = accessibilityService ?: return false
        val rootNode = service.rootInActiveWindow ?: return false

        try {
            val sendButtons = mutableListOf<AccessibilityNodeInfo>()
            findSendButtonsRecursively(rootNode, sendButtons)

            if (sendButtons.isNotEmpty()) {
                Log.d(TAG, "🎯 找到 ${sendButtons.size} 个可能的发送按钮")
                val firstButton = sendButtons.first()
                Log.d(TAG, "🎯 尝试点击第一个找到的发送按钮: ${firstButton.describe()}")

                if (firstButton.performAction(AccessibilityNodeInfo.ACTION_CLICK)) {
                    Log.d(TAG, "✅ 原生API点击发送按钮成功")
                    // 回收所有找到的节点
                    sendButtons.forEach { it.recycle() }
                    return true
                }
            }
            // 如果没有找到或点击失败，回收所有节点
            sendButtons.forEach { it.recycle() }
        } finally {
            rootNode.recycle()
        }

        Log.w(TAG, "❌ 原生API未找到可点击的发送按钮")
        return false
    }

    /**
     * 使用指定的最新节点树进行原生API发送按钮点击
     * @param latestRootNode 最新的根节点
     * @return 是否点击成功
     */
    private fun clickSendButtonWithNativeAPIUsingLatestNode(latestRootNode: AccessibilityNodeInfo): Boolean {
        Log.d(TAG, "🔍 使用最新节点树进行原生API查找发送按钮...")

        val sendButtons = mutableListOf<AccessibilityNodeInfo>()
        findSendButtonsRecursively(latestRootNode, sendButtons)

        if (sendButtons.isNotEmpty()) {
            Log.d(TAG, "🎯 在最新节点树中找到 ${sendButtons.size} 个可能的发送按钮")
            val firstButton = sendButtons.first()
            Log.d(TAG, "🎯 尝试点击第一个找到的发送按钮: ${firstButton.describe()}")

            if (firstButton.performAction(AccessibilityNodeInfo.ACTION_CLICK)) {
                Log.d(TAG, "✅ 使用最新节点的原生API点击发送按钮成功")
                // 回收所有找到的节点
                sendButtons.forEach { it.recycle() }
                return true
            }
        }
        // 如果没有找到或点击失败，回收所有节点
        sendButtons.forEach { it.recycle() }

        Log.w(TAG, "❌ 使用最新节点的原生API未找到可点击的发送按钮")
        return false
    }

    /**
     * 递归查找包含"发送"文本的可点击节点
     */
    private fun findSendButtonsRecursively(node: AccessibilityNodeInfo, results: MutableList<AccessibilityNodeInfo>) {
        val text = node.text?.toString()
        val desc = node.contentDescription?.toString()

        // 检查当前节点是否包含“发送”文本
        if (text?.contains("发送") == true || desc?.contains("发送") == true) {
            var current: AccessibilityNodeInfo? = node
            var clickableParent: AccessibilityNodeInfo? = null

            // 向上追溯，找到第一个可点击的父节点
            while (current != null) {
                if (current.isClickable) {
                    clickableParent = current
                    break
                }
                val parent = current.parent
                // 防止重复回收传递进来的原始节点
                if (current != node) {
                    current.recycle()
                }
                current = parent
            }

            if (clickableParent != null) {
                Log.d(TAG, "✅ 找到发送按钮候选: ${clickableParent.describe()}")
                results.add(clickableParent)
            } else {
                // 如果没有找到可点击的父节点，回收原始节点（如果它不是结果的一部分）
                if (node !in results) {
                    // node.recycle() // The caller of the top-level function will recycle the root
                }
                Log.w(TAG, "⚠️ 找到'发送'文本但其本身及父节点都不可点击: ${node.describe()}")
            }
        }

        // 递归检查子节点
        for (i in 0 until node.childCount) {
            node.getChild(i)?.let { child ->
                findSendButtonsRecursively(child, results)
                child.recycle()
            }
        }
    }

    /**
     * 递归查找发送相关的ImageButton或图标按钮
     */
    private fun findSendImageButtonsRecursively(node: AccessibilityNodeInfo, results: MutableList<AccessibilityNodeInfo>) {
        val className = node.className?.toString()
        val desc = node.contentDescription?.toString()

        // 检查是否是可能的发送图标按钮
        if (node.isClickable && (
            className?.contains("ImageButton") == true ||
            className?.contains("ImageView") == true ||
            desc?.contains("发送") == true ||
            desc?.contains("Send") == true
        )) {
            // 进一步检查是否在输入区域附近（简单的位置判断）
            val bounds = android.graphics.Rect()
            node.getBoundsInScreen(bounds)

            // 如果按钮在屏幕下半部分，更可能是发送按钮
            val screenHeight = android.content.res.Resources.getSystem().displayMetrics.heightPixels
            if (bounds.centerY() > screenHeight * 0.5) {
                results.add(node)
                Log.d(TAG, "🔍 找到发送图标按钮候选: ${node.describe()}")
            }
        }

        // 递归检查子节点
        for (i in 0 until node.childCount) {
            node.getChild(i)?.let { child ->
                findSendImageButtonsRecursively(child, results)
            }
        }
    }

    /**
     * 智能点击发送按钮 - 参考会话项点击的优化策略
     * 优化：利用EditText父组件的可点击特性
     * @param sendButton 发送按钮节点
     * @return 是否点击成功
     */
    private fun performSmartClickOnSendButton(sendButton: AccessibilityNodeInfo): Boolean {
        Log.d(TAG, "🎯 开始智能点击发送按钮")

        // 策略1: 查找发送按钮中心区域的可点击元素
        val centerClickable = findCenterClickableAreaForSendButton(sendButton)
        if (centerClickable != null) {
            val success = performClickOnNode(centerClickable)
            centerClickable.recycle()
            if (success) {
                Log.d(TAG, "✅ 发送按钮中心区域点击成功")
                return true
            }
        }

        // 策略2: 查找发送按钮内的文本区域（通常可点击）
        val textArea = sendButton.xpath(".//TextView[@text~='发送']").first()
        if (textArea != null) {
            val success = performClickOnNode(textArea)
            textArea.recycle()
            if (success) {
                Log.d(TAG, "✅ 发送按钮文本区域点击成功")
                return true
            }
        }

        // 策略3: 查找任何可点击的子元素
        val anyClickable = sendButton.xpath(".//[@clickable='true']").first()
        if (anyClickable != null) {
            val success = performClickOnNode(anyClickable)
            anyClickable.recycle()
            if (success) {
                Log.d(TAG, "✅ 发送按钮子元素点击成功")
                return true
            }
        }

        // 策略4: 直接点击发送按钮容器
        Log.d(TAG, "🎯 尝试直接点击发送按钮容器")
        return performClickOnNode(sendButton)
    }

    /**
     * 查找发送按钮中心区域的可点击元素
     * 参考会话项的中心区域查找逻辑
     */
    private fun findCenterClickableAreaForSendButton(sendButton: AccessibilityNodeInfo): AccessibilityNodeInfo? {
        val buttonBounds = Rect()
        sendButton.getBoundsInScreen(buttonBounds)

        val centerY = buttonBounds.centerY()
        val tolerance = buttonBounds.height() / 4 // 允许25%的误差

        // 查找Y坐标接近中心的可点击元素
        val clickableElements = sendButton.xpath(".//[@clickable='true']")

        for (element in clickableElements) {
            val elementBounds = Rect()
            element.getBoundsInScreen(elementBounds)

            if (Math.abs(elementBounds.centerY() - centerY) <= tolerance) {
                Log.d(TAG, "找到发送按钮中心区域可点击元素: ${element.describe()}")
                clickableElements.recycle()
                return element
            }
        }

        clickableElements.recycle()
        return null
    }

    /**
     * 模拟回车键手势
     * 注意：这个方法可能需要根据具体的键盘布局调整坐标
     */
    private fun simulateEnterKeyGesture(): Boolean {
        val service = accessibilityService ?: return false

        try {
            // 获取屏幕尺寸来计算回车键的大概位置
            val displayMetrics = context.resources.displayMetrics
            val screenWidth = displayMetrics.widthPixels
            val screenHeight = displayMetrics.heightPixels

            // 通常回车键在屏幕右下角区域，这里使用估算位置
            // 实际使用时可能需要根据具体键盘调整这些坐标
            val enterKeyX = (screenWidth * 0.9).toInt()  // 屏幕宽度的90%位置
            val enterKeyY = (screenHeight * 0.85).toInt() // 屏幕高度的85%位置

            Log.d(TAG, "🎯 尝试点击回车键位置: ($enterKeyX, $enterKeyY)")

            val path = Path().apply {
                moveTo(enterKeyX.toFloat(), enterKeyY.toFloat())
            }

            val gestureBuilder = GestureDescription.Builder()
            val strokeDescription = GestureDescription.StrokeDescription(path, 0, 100)
            gestureBuilder.addStroke(strokeDescription)

            val gesture = gestureBuilder.build()

            var gestureCompleted = false
            val success = service.dispatchGesture(gesture, object : AccessibilityService.GestureResultCallback() {
                override fun onCompleted(gestureDescription: GestureDescription?) {
                    gestureCompleted = true
                    Log.d(TAG, "✅ 回车键手势完成")
                }

                override fun onCancelled(gestureDescription: GestureDescription?) {
                    Log.w(TAG, "❌ 回车键手势被取消")
                }
            }, null)

            if (success) {
                // 等待手势完成
                val startTime = System.currentTimeMillis()
                while (!gestureCompleted && (System.currentTimeMillis() - startTime) < 1000) {
                    Thread.sleep(50)
                }
                return gestureCompleted
            }

        } catch (e: Exception) {
            Log.e(TAG, "模拟回车键手势时发生异常", e)
        }

        return false
    }

    /**
     * 增强的手势点击方法 - 添加重试机制
     */
    private fun performEnhancedGestureClick(x: Int, y: Int, retries: Int = 3): Boolean {
        for (attempt in 1..retries) {
            Log.d(TAG, "🎯 手势点击尝试 $attempt/$retries at ($x, $y)")

            if (performGestureClick(x, y)) {
                Log.d(TAG, "✅ 手势点击成功 (尝试 $attempt)")
                return true
            }

            if (attempt < retries) {
                Thread.sleep(300) // 重试前等待
            }
        }

        Log.e(TAG, "❌ 手势点击失败，已尝试 $retries 次")
        return false
    }

    /**
     * 调试方法：分析页面结构
     */
    fun analyzePageStructure(rootNode: AccessibilityNodeInfo) {
        Log.d(TAG, "🔍 分析页面结构")

        // 打印节点树结构
        Log.d(TAG, "🌳 页面节点树结构:")
        rootNode.printTree(maxDepth = 3, tag = TAG)

        // 查找所有可点击元素
        val clickableElements = rootNode.findClickable()
        Log.d(TAG, "找到 ${clickableElements.size} 个可点击元素:")
        clickableElements.forEach { element ->
            Log.d(TAG, "  - ${element.describe()}")
        }

        // 查找所有文本元素
        val textElements = rootNode.findAll(className = "TextView")
        Log.d(TAG, "找到 ${textElements.size} 个文本元素:")
        textElements.texts().forEach { text ->
            if (text.isNotBlank()) {
                Log.d(TAG, "  - '$text'")
            }
        }

        // 清理资源
        clickableElements.recycle()
        textElements.recycle()
    }

    // ===== 点击标记相关方法 =====

    /**
     * 显示点击标记
     */
    private fun showClickMarker(x: Int, y: Int) {
        if (!showClickMarker) return

        try {
            // 移除之前的标记
            removeClickMarker()

            // 创建新的标记视图
            markerView = View(context).apply {
                setBackgroundColor(Color.RED)
                alpha = 0.8f
            }

            val params = WindowManager.LayoutParams().apply {
                width = 20
                height = 20
                this.x = x - 10
                this.y = y - 10
                type = WindowManager.LayoutParams.TYPE_ACCESSIBILITY_OVERLAY
                flags = WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or
                        WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE or
                        WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN
                format = PixelFormat.TRANSLUCENT
                gravity = Gravity.TOP or Gravity.START
            }

            windowManager.addView(markerView, params)

            // 延时移除标记
            handler.postDelayed({
                removeClickMarker()
            }, CLICK_MARKER_DURATION)

        } catch (e: Exception) {
            Log.e(TAG, "显示点击标记失败", e)
        }
    }

    /**
     * 移除点击标记
     */
    private fun removeClickMarker() {
        markerView?.let { view ->
            // 只有当视图真正附加到窗口时才尝试移除
            if (view.isAttachedToWindow) {
                try {
                    windowManager.removeView(view)
                } catch (e: Exception) {
                    Log.w(TAG, "移除点击标记失败", e)
                }
            }
            markerView = null
        }
    }

    /**
     * 设置是否显示点击标记
     */
    fun setShowClickMarker(show: Boolean) {
        showClickMarker = show
        Log.d(TAG, "点击标记显示: $show")
    }

    /**
     * 在日志中显示会话概要
     */
    private fun displayChatSummaryInLog(summary: String) {
        val lines = summary.split("\n")
        Log.d(TAG, "会话记录总行数: ${lines.size}")

        lines.forEachIndexed { index, line ->
            if (line.trim().isNotEmpty()) {
                Log.d(TAG, "[$index] $line")
            }
        }

        // 统计信息
        val messageLines = lines.filter { it.startsWith("- ") }
        Log.d(TAG, "📊 统计信息: 共 ${messageLines.size} 条消息记录")
    }

    /**
     * 检查是否进入会话详情页
     * 使用xpath语法检查是否存在包含"发消息..."文本的EditText
     * 如果进入会话详情页，会自动获取会话概要
     * @return 是否在会话详情页
     */
    fun isInChatDetailPage(): Boolean {
        val service = accessibilityService
        if (service == null) {
            Log.e(TAG, "❌ AccessibilityService 未设置")
            return false
        }

        val rootNode = service.rootInActiveWindow
        if (rootNode == null) {
            Log.w(TAG, "❌ 无法获取根节点")
            return false
        }

        try {
            // 使用xpath查找包含"发消息..."文本的EditText
            val messageInputResult = rootNode.xpath("//EditText[@text='发消息...']")
            val isInChatPage = messageInputResult.exists()

            if (isInChatPage) {
                Log.d(TAG, "✅ 已进入会话详情页")
                // 进入会话详情页时，立即获取会话概要
                val summary = getChatHistorySummaryInternal(rootNode)
                if (summary != null) {
                    Log.d(TAG, "✅ 会话概要获取成功")
                } else {
                    Log.w(TAG, "⚠️ 会话概要获取失败")
                }
            }

            messageInputResult.recycle()
            return isInChatPage

        } catch (e: Exception) {
            Log.e(TAG, "检查会话详情页时发生异常", e)
            return false
        } finally {
            rootNode.recycle()
        }
    }

    /**
     * 获取会话记录概要
     * 从 //EditText[@text='发消息...']/../../../ScrollView[0] 获取文本作为会话记录的概要
     * @return 会话记录概要文本，如果获取失败返回null
     */
    fun getChatHistorySummary(): String? {
        val service = accessibilityService
        if (service == null) {
            Log.e(TAG, "❌ AccessibilityService 未设置")
            return null
        }

        val rootNode = service.rootInActiveWindow
        if (rootNode == null) {
            Log.w(TAG, "❌ 无法获取根节点")
            return null
        }

        try {
            return getChatHistorySummaryInternal(rootNode)
        } catch (e: Exception) {
            Log.e(TAG, "获取会话记录概要时发生异常", e)
            return null
        } finally {
            rootNode.recycle()
        }
    }

    /**
     * 内部方法：获取会话记录概要
     * @param rootNode 根节点，由调用方管理生命周期
     * @return 会话记录概要文本，如果获取失败返回null
     */
    private fun getChatHistorySummaryInternal(rootNode: AccessibilityNodeInfo): String? {
        try {
            // 1. 使用xpath查找会话记录容器
            val chatHistoryResult = rootNode.xpath("//EditText[@text='发消息...']/../../../ScrollView[0]/ViewGroup[0]")
            val chatHistoryContainer = chatHistoryResult.first()

            if (chatHistoryContainer == null) {
                Log.w(TAG, "❌ 未找到会话记录容器")
                chatHistoryResult.recycle()
                return null
            }

            Log.d(TAG, "✅ 找到会话记录容器，子节点数: ${chatHistoryContainer.childCount}")

            val messages = mutableListOf<String>()
            val childCount = chatHistoryContainer.childCount

            // 2. 遍历每个消息项 (ViewGroup[index])
            // 从后往前遍历，以实现消息反转
            for (i in (childCount - 1) downTo 0) {
                val messageItem = chatHistoryContainer.getChild(i)
                if (messageItem == null) {
                    Log.w(TAG, "警告: 索引 $i 的子节点为空")
                    continue
                }

                var messageBubbleNode: AccessibilityNodeInfo? = null
                var messageTextNode: AccessibilityNodeInfo? = null
                try {
                    // 3. 找到消息气泡节点，用于坐标判断
                    // 相对路径: ./ViewGroup[0]/ViewGroup[1]/ViewGroup[0]
                    messageBubbleNode = messageItem.xpath("/ViewGroup[0]/ViewGroup[1]/ViewGroup[0]").first()

                    if (messageBubbleNode == null) {
                        Log.w(TAG, "在消息项 $i 中未找到消息气泡节点 (/ViewGroup[0]/ViewGroup[1]/ViewGroup[0])")
                        continue
                    }

                    // 4. 从气泡节点中找到文本节点
                    // 相对路径: ./TextView[0]
                    messageTextNode = messageBubbleNode.xpath("/TextView[0]").first()
                    val messageText = messageTextNode?.text?.toString()?.trim()

                    if (messageText.isNullOrEmpty()) {
                        Log.w(TAG, "在消息项 $i 中未找到文本或文本为空")
                        continue
                    }

                    // 5. 根据坐标区分发送者
                    val bounds = Rect()
                    messageBubbleNode.getBoundsInScreen(bounds)

                    val sender = when {
                        bounds.left == 210 -> "客户"
                        bounds.right == 885 -> "客服"
                        else -> "未知" // 其他情况，例如系统消息
                    }
                    
                    // 6. 格式化并添加到列表
                    val formattedMessage = "$sender: $messageText"
                    messages.add(formattedMessage)
                    Log.d(TAG, "解析消息: $formattedMessage (Bounds: $bounds)")

                } finally {
                    // 回收在循环中创建的节点
                    messageBubbleNode?.recycle()
                    messageTextNode?.recycle()
                    messageItem.recycle()
                }
            }

            // 清理资源
            chatHistoryResult.recycle()

            return if (messages.isNotEmpty()) {
                messages.joinToString("\n")
            } else {
                "暂无会话记录"
            }

        } catch (e: Exception) {
            Log.e(TAG, "获取会话记录概要时发生异常", e)
            return null
        }
    }

    /**
     * 进入会话详情页时的处理方法
     * 检测到进入会话详情页时，立即获取会话概要并返回完整信息
     * @return 包含页面状态和会话概要的结果对象
     */
    fun onEnterChatDetailPage(): ChatDetailResult {
        val service = accessibilityService ?: return ChatDetailResult(false, null)
        val rootNode = service.rootInActiveWindow ?: return ChatDetailResult(false, null)

        try {
            val messageInputResult = rootNode.xpath("//EditText[@text='发消息...']")
            val isInChatPage = messageInputResult.exists()
            messageInputResult.recycle()

            if (!isInChatPage) {
                return ChatDetailResult(false, null)
            }

            // 仅获取概要，不执行其他操作
            val summary = getChatHistorySummaryInternal(rootNode)
            return ChatDetailResult(true, summary)

        } catch (e: Exception) {
            Log.e(TAG, "onEnterChatDetailPage error", e)
            return ChatDetailResult(false, null)
        } finally {
            rootNode.recycle()
        }
    }

    /**
     * 检查并获取会话详情信息
     * 综合方法：先检查是否在会话详情页，如果是则获取会话记录概要
     * @return 包含页面状态和会话概要的结果对象
     */
    fun checkAndGetChatDetails(): ChatDetailResult {
        Log.d(TAG, "🔍 === 开始检查会话详情 ===")

        val isInChatPage = isInChatDetailPage()
        val summary = if (isInChatPage) {
            // 注意：isInChatDetailPage()已经获取过概要了，这里避免重复获取
            Log.d(TAG, "ℹ️ 页面检查时已获取概要，跳过重复获取")
            null // 可以考虑缓存之前获取的结果
        } else {
            null
        }

        val result = ChatDetailResult(
            isInChatDetailPage = isInChatPage,
            chatHistorySummary = summary
        )

        Log.d(TAG, "🔍 === 会话详情检查完成 ===")
        Log.d(TAG, "页面状态: ${if (result.isInChatDetailPage) "会话详情页" else "非会话详情页"}")

        return result
    }

    /**
     * 清理资源
     */
    fun cleanup() {
        // 清理点击标记
        removeClickMarker()

        Log.d(TAG, "资源清理完成")
    }

    /**
     * 新方法：处理完整的聊天和回复流程
     */
    fun processChatAndReply() {
        Log.d(TAG, "🚀 === 开始完整的聊天和回复流程 ===")
        val service = accessibilityService ?: return
        val rootNode = service.rootInActiveWindow ?: return

        try {
            val summary = getChatHistorySummaryInternal(rootNode)
            if (summary == null) {
                Log.w(TAG, "⚠️ 无法获取会话概要，中止流程")
                return
            }

            Log.d(TAG, "✅ 成功获取会话概要，准备发送给 LLM")
            displayChatSummaryInLog(summary)

            CoroutineScope(Dispatchers.Main).launch {
                Log.d(TAG, "🤖 正在向 LLM 发送会话概要...")
                val response = openAIService.getCompletion(summary)

                if (response != null && !response.startsWith("错误")) {
                    Log.d(TAG, "🤖 LLM 返回结果: $response")
                    val inputSuccess = handleMessageInput(rootNode, response)
                    if (inputSuccess) {
                        Log.d(TAG, "✅ 消息已发送，准备返回...")
                        kotlinx.coroutines.delay(1000)
                        service.performGlobalAction(AccessibilityService.GLOBAL_ACTION_BACK)
                    } else {
                        Log.e(TAG, "❌ 发送 LLM 返回的消息失败")
                    }
                } else {
                    Log.e(TAG, "❌ 从 LLM 获取结果失败: $response")
                }
            }
        } finally {
            rootNode.recycle()
        }
    }
}

/**
 * 会话详情检查结果数据类
 */
data class ChatDetailResult(
    val isInChatDetailPage: Boolean,
    val chatHistorySummary: String?
)

// ===== 扩展函数：为客服场景定制的便捷方法 =====

/**
 * 查找发送按钮的多种策略
 */
