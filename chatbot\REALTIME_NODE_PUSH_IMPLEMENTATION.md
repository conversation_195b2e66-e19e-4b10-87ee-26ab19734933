# 实时节点推送机制实现

## 概述

实现了类似WebViewService的实时节点推送机制，让CustomerServiceHandler中的策略10也能使用被实时推送的节点树，而不是临时获取。

## 实现架构

### 1. 数据流向
```
ChatbotAccessibilityService (数据源)
    ↓ onAccessibilityEvent
    ↓ rootInActiveWindow
    ├─→ WebViewService.updateRootNode()
    └─→ CustomerServiceHandler.updateRootNode()
```

### 2. 存储机制
参考WebViewService的设计，在CustomerServiceHandler中添加：

```kotlin
// 实时节点推送机制 - 参考WebViewService的设计
private val currentRootNode = AtomicReference<AccessibilityNodeInfo?>(null)

/**
 * 更新当前根节点 - 实时推送机制
 * 参考WebViewService的设计，由AccessibilityService主动推送最新节点
 */
fun updateRootNode(rootNode: AccessibilityNodeInfo?) {
    // Create a copy of the node to prevent issues with recycling
    val oldNode = currentRootNode.getAndSet(rootNode?.let { AccessibilityNodeInfo(it) })
    oldNode?.recycle()
    Log.d(TAG, "🔄 节点已更新: ${if (rootNode != null) "有效节点" else "空节点"}")
}
```

## 核心特性

### 1. 被动接收模式
- **实时推送**: 由ChatbotAccessibilityService主动推送最新节点
- **自动更新**: 每次UI变化时自动更新存储的节点
- **无需主动获取**: 策略10直接使用存储的节点

### 2. 双重保障机制
```kotlin
val strategy10RootNode = getCurrentRootNode()
if (strategy10RootNode == null) {
    // 如果没有推送节点，作为备用方案主动获取一次
    val fallbackRootNode = service.rootInActiveWindow
    // 更新推送节点并同步到WebViewService
    updateRootNode(fallbackRootNode)
}
```

### 3. 与WebViewService同步
```kotlin
// 同时更新CustomerServiceHandler中的节点信息，实现实时推送
customerServiceHandler?.updateRootNode(rootInActiveWindow)
```

## 技术优势

### 1. 性能优化
- **减少获取次数**: 不需要在策略10执行时临时获取节点
- **实时性**: 节点信息始终是最新的
- **缓存机制**: 避免重复的rootInActiveWindow调用

### 2. 一致性保证
- **状态同步**: CustomerServiceHandler和WebViewService看到相同的节点状态
- **时序一致**: 所有组件使用相同时间点的节点信息
- **数据源统一**: 都来自同一个AccessibilityService

### 3. 可靠性提升
- **备用机制**: 如果推送失败，仍有主动获取的备用方案
- **资源管理**: 自动回收旧节点，防止内存泄漏
- **错误处理**: 完善的异常处理和日志记录

## 实现细节

### 1. 节点存储
```kotlin
private val currentRootNode = AtomicReference<AccessibilityNodeInfo?>(null)
```
- 使用AtomicReference确保线程安全
- 自动回收旧节点防止内存泄漏

### 2. 推送机制
```kotlin
// 在ChatbotAccessibilityService中
customerServiceHandler?.updateRootNode(rootInActiveWindow)
```
- 每次accessibility事件时自动推送
- 与WebViewService推送同步进行

### 3. 策略10优化
```kotlin
// 使用推送的节点
val strategy10RootNode = getCurrentRootNode()
if (strategy10RootNode == null) {
    // 备用方案：主动获取
    val fallbackRootNode = service.rootInActiveWindow
    updateRootNode(fallbackRootNode)
}
```

## 使用场景

### 1. 主要场景
- 策略10执行时直接使用推送的节点
- 实时UI状态监控
- 多组件节点状态同步

### 2. 备用场景
- 推送节点为空时的主动获取
- 初始化阶段的节点获取
- 异常情况下的恢复机制

## 对比分析

### 优化前（临时获取）
```kotlin
// 策略10前临时获取
val strategy10RootNode = service.rootInActiveWindow
```
- **缺点**: 每次都需要系统调用
- **缺点**: 可能获取到过期状态
- **缺点**: 增加执行延迟

### 优化后（实时推送）
```kotlin
// 使用实时推送的节点
val strategy10RootNode = getCurrentRootNode()
```
- **优点**: 无需系统调用，直接使用缓存
- **优点**: 节点信息实时更新
- **优点**: 执行速度更快

## 监控和调试

### 1. 日志记录
```kotlin
Log.d(TAG, "🔄 节点已更新: ${if (rootNode != null) "有效节点" else "空节点"}")
Log.d(TAG, "✅ 策略10: 使用实时推送的节点树")
```

### 2. 状态追踪
- 推送节点的更新时间
- 策略10的执行状态
- 备用机制的触发情况

### 3. WebViewService同步
- 可通过WebViewService查看当前节点状态
- 验证推送机制的正确性
- 调试节点信息的一致性

## 总结

通过实现实时节点推送机制，策略10现在可以使用被动接收的最新节点信息，而不需要临时获取。这提高了执行效率，确保了状态一致性，并提供了与WebViewService同步的调试能力。同时保留了备用的主动获取机制，确保在任何情况下都能正常工作。
