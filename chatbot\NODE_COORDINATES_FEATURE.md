# 节点坐标显示功能

## 🎯 功能概述

在节点层级列表中增加了节点坐标显示功能，让开发者能够更直观地了解每个UI元素在屏幕上的位置和大小信息。

## ✨ 新增功能

### 1. 坐标信息显示
- **屏幕坐标**: 显示节点在屏幕上的绝对位置 `(left, top) → (right, bottom)`
- **尺寸信息**: 显示节点的宽度和高度 `width×height`
- **可视化标识**: 使用蓝色背景突出显示坐标信息

### 2. 增强的属性显示
- **图标化属性**: 使用emoji图标让属性更直观
  - 🖱️ 可点击
  - 📜 可滚动  
  - ☑️ 可选中
  - ✅ 已选中
  - 🟢 已启用
  - 🎯 已聚焦
  - 🔘 已选择
  - 👻 不可见

### 3. 改进的信息布局
- **多行显示**: 信息分行显示，更易阅读
- **层次化结构**: 类名、坐标、文本、描述、ID分层显示
- **样式优化**: 使用不同颜色和字体突出重要信息

## 🎨 显示效果

### 之前的显示格式：
```
TextView - "Hello World" [desc: "greeting"] [id: text_hello]
```

### 现在的显示格式：
```
TextView
坐标: (100, 200) → (500, 250)  大小: 400×50
📝 "Hello World"
🏷️ [desc: "greeting"]
🆔 [id: text_hello]
🖱️ 可点击 🟢 已启用
```

## 🔧 技术实现

### 1. 坐标获取
```kotlin
val bounds = android.graphics.Rect()
node.getBoundsInScreen(bounds)
if (!bounds.isEmpty) {
    sb.append("<span class=\"coordinates\">坐标: (${bounds.left}, ${bounds.top}) → (${bounds.right}, ${bounds.bottom})</span>")
    sb.append("<span class=\"coordinates\">大小: ${bounds.width()}×${bounds.height()}</span>")
}
```

### 2. 属性图标化
```kotlin
val attributes = mutableListOf<String>()
if (node.isClickable) attributes.add("🖱️ 可点击")
if (node.isScrollable) attributes.add("📜 可滚动")
if (node.isCheckable) attributes.add("☑️ 可选中")
// ... 更多属性
```

### 3. CSS样式优化
```css
.coordinates { 
    color: #007bff; 
    font-weight: 600; 
    background-color: #e7f3ff; 
    padding: 2px 6px; 
    border-radius: 4px; 
    margin: 0 4px; 
    font-size: 12px; 
}

.attributes { 
    color: #28a745; 
    font-weight: 500; 
    font-size: 12px; 
}
```

## 📊 信息层次

### 第一层：基本信息
- **类名**: 粗体显示，如 `TextView`、`Button`
- **坐标**: 蓝色背景高亮显示

### 第二层：内容信息  
- **文本内容**: 📝 图标 + 斜体文本
- **内容描述**: 🏷️ 图标 + 小字体
- **资源ID**: 🆔 图标 + 小字体

### 第三层：状态属性
- **交互属性**: 🖱️ 可点击、📜 可滚动等
- **状态属性**: ✅ 已选中、🎯 已聚焦等

## 🚀 使用场景

### 1. UI自动化测试
- **精确定位**: 通过坐标信息精确定位元素
- **边界检查**: 验证元素是否在预期位置
- **尺寸验证**: 检查元素大小是否符合设计要求

### 2. 点击位置优化
- **中心点计算**: `centerX = (left + right) / 2`
- **避免边缘**: 选择元素内部安全区域进行点击
- **多位置策略**: 根据元素大小选择最佳点击位置

### 3. 布局分析
- **重叠检测**: 发现元素重叠问题
- **对齐检查**: 验证元素对齐情况
- **间距测量**: 计算元素间距

## 📱 实际应用示例

### 示例1: 按钮点击优化
```
Button
坐标: (300, 800) → (500, 900)  大小: 200×100
📝 "发送"
🖱️ 可点击 🟢 已启用

// 计算最佳点击位置
centerX = (300 + 500) / 2 = 400
centerY = (800 + 900) / 2 = 850
```

### 示例2: 列表项定位
```
ViewGroup
坐标: (50, 200) → (750, 300)  大小: 700×100
🖱️ 可点击

  TextView
  坐标: (70, 220) → (300, 250)  大小: 230×30
  📝 "用户昵称"
  
  TextView  
  坐标: (70, 260) → (400, 280)  大小: 330×20
  📝 "最后一条消息内容"
```

### 示例3: 输入框识别
```
EditText
坐标: (100, 1000) → (600, 1080)  大小: 500×80
🏷️ [desc: "输入消息"]
🆔 [id: message_input]
🖱️ 可点击 🟢 已启用 🎯 已聚焦
```

## 🔍 调试优势

### 1. 可视化调试
- **位置验证**: 直观看到元素在屏幕上的位置
- **大小检查**: 确认元素尺寸是否合理
- **状态确认**: 快速识别元素的交互状态

### 2. 问题定位
- **点击失败**: 通过坐标信息分析点击位置是否正确
- **元素遮挡**: 发现元素被其他元素遮挡的情况
- **边界问题**: 识别元素边界异常的情况

### 3. 性能优化
- **精确查找**: 使用坐标信息优化元素查找策略
- **区域限制**: 在特定区域内搜索元素，提高效率
- **批量操作**: 根据位置信息批量处理相似元素

## 🎉 总结

通过添加节点坐标显示功能，我们实现了：

1. **✅ 更直观的信息展示** - 坐标、尺寸、属性一目了然
2. **✅ 更好的调试体验** - 快速定位和分析UI元素
3. **✅ 更精确的自动化** - 基于坐标信息优化点击策略
4. **✅ 更美观的界面** - 图标化属性和层次化布局

这个功能将大大提升UI自动化开发和调试的效率！🚀

## 📝 使用方法

1. 启动应用并开启无障碍服务
2. 打开浏览器访问 `http://localhost:8080`
3. 查看节点层级列表，现在每个节点都会显示详细的坐标和属性信息
4. 使用坐标信息优化您的自动化脚本
