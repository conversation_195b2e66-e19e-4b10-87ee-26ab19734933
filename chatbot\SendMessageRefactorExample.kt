package com.coffee.chatbot.example

import android.accessibilityservice.AccessibilityService
import android.content.Context
import android.util.Log
import com.coffee.chatbot.service.CustomerServiceHandler
import com.coffee.chatbot.service.OpenAIService

/**
 * 发送功能重构使用示例
 * 
 * 展示如何使用重构后的 handleMessageInput 方法
 * 该方法现在支持：
 * 1. 激活EditText焦点
 * 2. 发送回车消息（多种策略）
 */
class SendMessageRefactorExample {
    
    companion object {
        private const val TAG = "SendMessageExample"
    }
    
    private lateinit var customerServiceHandler: CustomerServiceHandler
    private lateinit var accessibilityService: AccessibilityService
    
    /**
     * 初始化示例
     */
    fun initialize(context: Context, openAIService: OpenAIService, service: AccessibilityService) {
        customerServiceHandler = CustomerServiceHandler(context, openAIService)
        customerServiceHandler.setAccessibilityService(service)
        accessibilityService = service
        
        Log.d(TAG, "✅ SendMessageRefactorExample 初始化完成")
    }
    
    /**
     * 示例1: 基本消息发送
     * 展示重构后的发送功能基本用法
     */
    fun basicMessageSendExample() {
        Log.d(TAG, "🚀 === 基本消息发送示例 ===")
        
        val rootNode = accessibilityService.rootInActiveWindow
        if (rootNode == null) {
            Log.e(TAG, "❌ 无法获取根节点")
            return
        }
        
        try {
            // 使用重构后的发送功能
            val message = "您好，我是智能客服助手，请问有什么可以帮助您的吗？"
            val success = customerServiceHandler.handleMessageInput(rootNode, message)
            
            if (success) {
                Log.d(TAG, "✅ 消息发送成功: $message")
            } else {
                Log.e(TAG, "❌ 消息发送失败: $message")
            }
            
        } finally {
            rootNode.recycle()
        }
    }
    
    /**
     * 示例2: 批量消息发送
     * 展示如何发送多条消息
     */
    fun batchMessageSendExample() {
        Log.d(TAG, "🚀 === 批量消息发送示例 ===")
        
        val messages = listOf(
            "感谢您的咨询！",
            "我们会尽快为您处理。",
            "如有其他问题，请随时联系我们。"
        )
        
        val rootNode = accessibilityService.rootInActiveWindow
        if (rootNode == null) {
            Log.e(TAG, "❌ 无法获取根节点")
            return
        }
        
        try {
            var successCount = 0
            
            for ((index, message) in messages.withIndex()) {
                Log.d(TAG, "📤 发送第 ${index + 1} 条消息: $message")
                
                val success = customerServiceHandler.handleMessageInput(rootNode, message)
                if (success) {
                    successCount++
                    Log.d(TAG, "✅ 第 ${index + 1} 条消息发送成功")
                    
                    // 消息间隔，避免发送过快
                    Thread.sleep(1000)
                } else {
                    Log.e(TAG, "❌ 第 ${index + 1} 条消息发送失败")
                    break // 如果某条消息发送失败，停止后续发送
                }
            }
            
            Log.d(TAG, "📊 批量发送完成: $successCount/${messages.size} 条消息成功")
            
        } finally {
            rootNode.recycle()
        }
    }
    
    /**
     * 示例3: 智能回复示例
     * 展示在完整聊天流程中的使用
     */
    fun smartReplyExample() {
        Log.d(TAG, "🚀 === 智能回复示例 ===")
        
        // 检查是否在聊天详情页
        if (!customerServiceHandler.isInChatDetailPage()) {
            Log.w(TAG, "❌ 不在聊天详情页，无法进行智能回复")
            return
        }
        
        // 获取聊天记录概要
        val chatSummary = customerServiceHandler.getChatHistorySummary()
        if (chatSummary == null) {
            Log.w(TAG, "❌ 无法获取聊天记录概要")
            return
        }
        
        Log.d(TAG, "✅ 获取到聊天记录概要，长度: ${chatSummary.length}")
        
        // 使用完整的聊天和回复流程
        // 这个方法内部会使用重构后的 handleMessageInput
        customerServiceHandler.processChatAndReply()
    }
    
    /**
     * 示例4: 错误处理示例
     * 展示如何处理发送失败的情况
     */
    fun errorHandlingExample() {
        Log.d(TAG, "🚀 === 错误处理示例 ===")
        
        val rootNode = accessibilityService.rootInActiveWindow
        if (rootNode == null) {
            Log.e(TAG, "❌ 无法获取根节点")
            return
        }
        
        try {
            val message = "测试消息"
            val maxRetries = 3
            var success = false
            
            for (attempt in 1..maxRetries) {
                Log.d(TAG, "🎯 第 $attempt 次尝试发送消息")
                
                success = customerServiceHandler.handleMessageInput(rootNode, message)
                
                if (success) {
                    Log.d(TAG, "✅ 第 $attempt 次尝试成功")
                    break
                } else {
                    Log.w(TAG, "⚠️ 第 $attempt 次尝试失败")
                    
                    if (attempt < maxRetries) {
                        Log.d(TAG, "⏳ 等待 2 秒后重试...")
                        Thread.sleep(2000)
                    }
                }
            }
            
            if (!success) {
                Log.e(TAG, "❌ 所有重试都失败了，消息发送失败")
                // 这里可以添加其他错误处理逻辑
                // 例如：记录错误、通知用户、切换到备用方案等
            }
            
        } finally {
            rootNode.recycle()
        }
    }
    
    /**
     * 示例5: 性能监控示例
     * 展示如何监控发送性能
     */
    fun performanceMonitoringExample() {
        Log.d(TAG, "🚀 === 性能监控示例 ===")
        
        val rootNode = accessibilityService.rootInActiveWindow
        if (rootNode == null) {
            Log.e(TAG, "❌ 无法获取根节点")
            return
        }
        
        try {
            val message = "性能测试消息"
            val startTime = System.currentTimeMillis()
            
            Log.d(TAG, "⏱️ 开始发送消息: $startTime")
            
            val success = customerServiceHandler.handleMessageInput(rootNode, message)
            
            val endTime = System.currentTimeMillis()
            val duration = endTime - startTime
            
            Log.d(TAG, "⏱️ 消息发送完成: $endTime")
            Log.d(TAG, "📊 发送耗时: ${duration}ms")
            Log.d(TAG, "📊 发送结果: ${if (success) "成功" else "失败"}")
            
            // 性能分析
            when {
                duration < 1000 -> Log.d(TAG, "🚀 发送速度: 优秀 (<1s)")
                duration < 3000 -> Log.d(TAG, "✅ 发送速度: 良好 (1-3s)")
                duration < 5000 -> Log.w(TAG, "⚠️ 发送速度: 一般 (3-5s)")
                else -> Log.e(TAG, "❌ 发送速度: 较慢 (>5s)")
            }
            
        } finally {
            rootNode.recycle()
        }
    }
    
    /**
     * 运行所有示例
     */
    fun runAllExamples() {
        Log.d(TAG, "🎬 === 开始运行所有示例 ===")
        
        try {
            basicMessageSendExample()
            Thread.sleep(2000)
            
            batchMessageSendExample()
            Thread.sleep(2000)
            
            smartReplyExample()
            Thread.sleep(2000)
            
            errorHandlingExample()
            Thread.sleep(2000)
            
            performanceMonitoringExample()
            
        } catch (e: Exception) {
            Log.e(TAG, "运行示例时发生异常", e)
        }
        
        Log.d(TAG, "🎬 === 所有示例运行完成 ===")
    }
}
