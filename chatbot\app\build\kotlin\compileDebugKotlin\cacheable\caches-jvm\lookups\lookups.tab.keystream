  ic_media_pause android.R.drawable  
ic_media_play android.R.drawable  ic_menu_search android.R.drawable  AccessibilityService android.accessibilityservice  AccessibilityServiceInfo android.accessibilityservice  GestureDescription android.accessibilityservice  AccessibilityEvent 1android.accessibilityservice.AccessibilityService  AccessibilityNodeInfo 1android.accessibilityservice.AccessibilityService  Bundle 1android.accessibilityservice.AccessibilityService  ChatMessage 1android.accessibilityservice.AccessibilityService  ChatbotAccessibilityService 1android.accessibilityservice.AccessibilityService  ConcurrentHashMap 1android.accessibilityservice.AccessibilityService  CoroutineScope 1android.accessibilityservice.AccessibilityService  CustomerServiceHandler 1android.accessibilityservice.AccessibilityService  Dispatchers 1android.accessibilityservice.AccessibilityService  	Exception 1android.accessibilityservice.AccessibilityService  GLOBAL_ACTION_BACK 1android.accessibilityservice.AccessibilityService  GestureResult<PERSON>allback 1android.accessibilityservice.AccessibilityService  Handler 1android.accessibilityservice.AccessibilityService  Intent 1android.accessibilityservice.AccessibilityService  Log 1android.accessibilityservice.AccessibilityService  Looper 1android.accessibilityservice.AccessibilityService  MessageGroup 1android.accessibilityservice.AccessibilityService  Rect 1android.accessibilityservice.AccessibilityService  Regex 1android.accessibilityservice.AccessibilityService  Runnable 1android.accessibilityservice.AccessibilityService  Settings 1android.accessibilityservice.AccessibilityService  String 1android.accessibilityservice.AccessibilityService  
SupervisorJob 1android.accessibilityservice.AccessibilityService  TAG 1android.accessibilityservice.AccessibilityService  	TextUtils 1android.accessibilityservice.AccessibilityService  WebViewService 1android.accessibilityservice.AccessibilityService  any 1android.accessibilityservice.AccessibilityService  apply 1android.accessibilityservice.AccessibilityService  cancel 1android.accessibilityservice.AccessibilityService  chatHistoryCallback 1android.accessibilityservice.AccessibilityService  contains 1android.accessibilityservice.AccessibilityService  delay 1android.accessibilityservice.AccessibilityService  dispatchGesture 1android.accessibilityservice.AccessibilityService  equals 1android.accessibilityservice.AccessibilityService  extractChatHistory 1android.accessibilityservice.AccessibilityService  findNodeByClassName 1android.accessibilityservice.AccessibilityService  findNodeByText 1android.accessibilityservice.AccessibilityService  findScrollableNode 1android.accessibilityservice.AccessibilityService  getInstance 1android.accessibilityservice.AccessibilityService  handleCustomerServicePage 1android.accessibilityservice.AccessibilityService  instance 1android.accessibilityservice.AccessibilityService  isBlank 1android.accessibilityservice.AccessibilityService  isExtractionRunning 1android.accessibilityservice.AccessibilityService  java 1android.accessibilityservice.AccessibilityService  launch 1android.accessibilityservice.AccessibilityService  listOf 1android.accessibilityservice.AccessibilityService  matches 1android.accessibilityservice.AccessibilityService  
mutableListOf 1android.accessibilityservice.AccessibilityService  	onDestroy 1android.accessibilityservice.AccessibilityService  onServiceConnected 1android.accessibilityservice.AccessibilityService  performGlobalAction 1android.accessibilityservice.AccessibilityService  rootInActiveWindow 1android.accessibilityservice.AccessibilityService  set 1android.accessibilityservice.AccessibilityService  sortedBy 1android.accessibilityservice.AccessibilityService  
startsWith 1android.accessibilityservice.AccessibilityService  until 1android.accessibilityservice.AccessibilityService  withContext 1android.accessibilityservice.AccessibilityService  Log Gandroid.accessibilityservice.AccessibilityService.GestureResultCallback  TAG Gandroid.accessibilityservice.AccessibilityService.GestureResultCallback  Builder /android.accessibilityservice.GestureDescription  StrokeDescription /android.accessibilityservice.GestureDescription  	addStroke 7android.accessibilityservice.GestureDescription.Builder  build 7android.accessibilityservice.GestureDescription.Builder  Activity android.app  Notification android.app  NotificationChannel android.app  NotificationManager android.app  Service android.app  ActivityResultContracts android.app.Activity  Build android.app.Activity  ChatbotAccessibilityService android.app.Activity  ChatbotTheme android.app.Activity  	Exception android.app.Activity  FloatingWindowService android.app.Activity  Intent android.app.Activity  Log android.app.Activity  MainContent android.app.Activity  Modifier android.app.Activity  Scaffold android.app.Activity  Settings android.app.Activity  	TextUtils android.app.Activity  Toast android.app.Activity  Uri android.app.Activity  WebViewService android.app.Activity  enableEdgeToEdge android.app.Activity  equals android.app.Activity  fillMaxSize android.app.Activity  getLocalIpAddress android.app.Activity  getValue android.app.Activity  java android.app.Activity  mutableStateOf android.app.Activity  onCreate android.app.Activity  onResume android.app.Activity  padding android.app.Activity  provideDelegate android.app.Activity  
setContent android.app.Activity  setValue android.app.Activity  
startActivity android.app.Activity  apply android.app.NotificationChannel  description android.app.NotificationChannel  IMPORTANCE_LOW android.app.NotificationManager  createNotificationChannel android.app.NotificationManager  AccessibilityEvent android.app.Service  AccessibilityNodeInfo android.app.Service  AtomicReference android.app.Service  Build android.app.Service  Bundle android.app.Service  
CHANNEL_ID android.app.Service  ChatMessage android.app.Service  ChatbotAccessibilityService android.app.Service  ConcurrentHashMap android.app.Service  Context android.app.Service  CoroutineScope android.app.Service  CustomerServiceHandler android.app.Service  DEFAULT_PORT android.app.Service  Dispatchers android.app.Service  	Exception android.app.Service  GLOBAL_ACTION_BACK android.app.Service  Gravity android.app.Service  Handler android.app.Service  IOException android.app.Service  IllegalArgumentException android.app.Service  ImageButton android.app.Service  Intent android.app.Service  LayoutInflater android.app.Service  Log android.app.Service  Looper android.app.Service  Math android.app.Service  MessageGroup android.app.Service  MotionEvent android.app.Service  NOTIFICATION_ID android.app.Service  NetworkInterface android.app.Service  NotificationChannel android.app.Service  NotificationCompat android.app.Service  NotificationManager android.app.Service  PixelFormat android.app.Service  R android.app.Service  Rect android.app.Service  Regex android.app.Service  Response android.app.Service  Runnable android.app.Service  START_NOT_STICKY android.app.Service  START_STICKY android.app.Service  Settings android.app.Service  String android.app.Service  
StringBuilder android.app.Service  
SupervisorJob android.app.Service  TAG android.app.Service  	TextUtils android.app.Service  Toast android.app.Service  WebViewService android.app.Service  
WindowManager android.app.Service  android android.app.Service  any android.app.Service  apply android.app.Service  
asSequence android.app.Service  cancel android.app.Service  chatHistoryCallback android.app.Service  contains android.app.Service  delay android.app.Service  equals android.app.Service  extractChatHistory android.app.Service  findNodeByClassName android.app.Service  findNodeByText android.app.Service  findScrollableNode android.app.Service  floatingView android.app.Service  forEach android.app.Service  
forceStart android.app.Service  generateHtml android.app.Service  getInstance android.app.Service  getLocalIpAddress android.app.Service  handleCustomerServicePage android.app.Service  indexOf android.app.Service  instance android.app.Service  isAccessibilityServiceEnabled android.app.Service  isAutomationRunning android.app.Service  isBlank android.app.Service  isExtractionRunning android.app.Service  
isInitialized android.app.Service  isMonitoringMode android.app.Service  java android.app.Service  launch android.app.Service  let android.app.Service  listOf android.app.Service  matches android.app.Service  
mutableListOf android.app.Service  newFixedLengthResponse android.app.Service  onCreate android.app.Service  	onDestroy android.app.Service  repeat android.app.Service  replace android.app.Service  	resources android.app.Service  rootInActiveWindow android.app.Service  set android.app.Service  sortedBy android.app.Service  
startActivity android.app.Service  startForeground android.app.Service  
startsWith android.app.Service  stopSelf android.app.Service  substringAfterLast android.app.Service  until android.app.Service  withContext android.app.Service  
ComponentName android.content  Context android.content  Intent android.content  AccessibilityEvent android.content.Context  AccessibilityNodeInfo android.content.Context  ActivityResultContracts android.content.Context  AtomicReference android.content.Context  Build android.content.Context  Bundle android.content.Context  
CHANNEL_ID android.content.Context  ChatMessage android.content.Context  ChatbotAccessibilityService android.content.Context  ChatbotTheme android.content.Context  ConcurrentHashMap android.content.Context  Context android.content.Context  CoroutineScope android.content.Context  CustomerServiceHandler android.content.Context  DEFAULT_PORT android.content.Context  Dispatchers android.content.Context  	Exception android.content.Context  FloatingWindowService android.content.Context  GLOBAL_ACTION_BACK android.content.Context  Gravity android.content.Context  Handler android.content.Context  IOException android.content.Context  IllegalArgumentException android.content.Context  ImageButton android.content.Context  Intent android.content.Context  LayoutInflater android.content.Context  Log android.content.Context  Looper android.content.Context  MainContent android.content.Context  Math android.content.Context  MessageGroup android.content.Context  Modifier android.content.Context  MotionEvent android.content.Context  NOTIFICATION_ID android.content.Context  NetworkInterface android.content.Context  NotificationChannel android.content.Context  NotificationCompat android.content.Context  NotificationManager android.content.Context  PixelFormat android.content.Context  R android.content.Context  Rect android.content.Context  Regex android.content.Context  Response android.content.Context  Runnable android.content.Context  START_NOT_STICKY android.content.Context  START_STICKY android.content.Context  Scaffold android.content.Context  Settings android.content.Context  String android.content.Context  
StringBuilder android.content.Context  
SupervisorJob android.content.Context  TAG android.content.Context  	TextUtils android.content.Context  Toast android.content.Context  Uri android.content.Context  WINDOW_SERVICE android.content.Context  WebViewService android.content.Context  
WindowManager android.content.Context  android android.content.Context  any android.content.Context  apply android.content.Context  
asSequence android.content.Context  cancel android.content.Context  chatHistoryCallback android.content.Context  contains android.content.Context  contentResolver android.content.Context  delay android.content.Context  enableEdgeToEdge android.content.Context  equals android.content.Context  extractChatHistory android.content.Context  fillMaxSize android.content.Context  findNodeByClassName android.content.Context  findNodeByText android.content.Context  findScrollableNode android.content.Context  floatingView android.content.Context  forEach android.content.Context  
forceStart android.content.Context  generateHtml android.content.Context  getInstance android.content.Context  getLocalIpAddress android.content.Context  getSystemService android.content.Context  getValue android.content.Context  handleCustomerServicePage android.content.Context  indexOf android.content.Context  instance android.content.Context  isAccessibilityServiceEnabled android.content.Context  isAutomationRunning android.content.Context  isBlank android.content.Context  isExtractionRunning android.content.Context  
isInitialized android.content.Context  isMonitoringMode android.content.Context  java android.content.Context  launch android.content.Context  let android.content.Context  listOf android.content.Context  matches android.content.Context  
mutableListOf android.content.Context  mutableStateOf android.content.Context  newFixedLengthResponse android.content.Context  packageName android.content.Context  padding android.content.Context  provideDelegate android.content.Context  repeat android.content.Context  replace android.content.Context  	resources android.content.Context  rootInActiveWindow android.content.Context  set android.content.Context  
setContent android.content.Context  setValue android.content.Context  sortedBy android.content.Context  
startActivity android.content.Context  
startsWith android.content.Context  substringAfterLast android.content.Context  until android.content.Context  withContext android.content.Context  AccessibilityEvent android.content.ContextWrapper  AccessibilityNodeInfo android.content.ContextWrapper  ActivityResultContracts android.content.ContextWrapper  AtomicReference android.content.ContextWrapper  Build android.content.ContextWrapper  Bundle android.content.ContextWrapper  
CHANNEL_ID android.content.ContextWrapper  ChatMessage android.content.ContextWrapper  ChatbotAccessibilityService android.content.ContextWrapper  ChatbotTheme android.content.ContextWrapper  ConcurrentHashMap android.content.ContextWrapper  Context android.content.ContextWrapper  CoroutineScope android.content.ContextWrapper  CustomerServiceHandler android.content.ContextWrapper  DEFAULT_PORT android.content.ContextWrapper  Dispatchers android.content.ContextWrapper  	Exception android.content.ContextWrapper  FloatingWindowService android.content.ContextWrapper  GLOBAL_ACTION_BACK android.content.ContextWrapper  Gravity android.content.ContextWrapper  Handler android.content.ContextWrapper  IOException android.content.ContextWrapper  IllegalArgumentException android.content.ContextWrapper  ImageButton android.content.ContextWrapper  Intent android.content.ContextWrapper  LayoutInflater android.content.ContextWrapper  Log android.content.ContextWrapper  Looper android.content.ContextWrapper  MainContent android.content.ContextWrapper  Math android.content.ContextWrapper  MessageGroup android.content.ContextWrapper  Modifier android.content.ContextWrapper  MotionEvent android.content.ContextWrapper  NOTIFICATION_ID android.content.ContextWrapper  NetworkInterface android.content.ContextWrapper  NotificationChannel android.content.ContextWrapper  NotificationCompat android.content.ContextWrapper  NotificationManager android.content.ContextWrapper  PixelFormat android.content.ContextWrapper  R android.content.ContextWrapper  Rect android.content.ContextWrapper  Regex android.content.ContextWrapper  Response android.content.ContextWrapper  Runnable android.content.ContextWrapper  START_NOT_STICKY android.content.ContextWrapper  START_STICKY android.content.ContextWrapper  Scaffold android.content.ContextWrapper  Settings android.content.ContextWrapper  String android.content.ContextWrapper  
StringBuilder android.content.ContextWrapper  
SupervisorJob android.content.ContextWrapper  TAG android.content.ContextWrapper  	TextUtils android.content.ContextWrapper  Toast android.content.ContextWrapper  Uri android.content.ContextWrapper  WebViewService android.content.ContextWrapper  
WindowManager android.content.ContextWrapper  android android.content.ContextWrapper  any android.content.ContextWrapper  applicationContext android.content.ContextWrapper  apply android.content.ContextWrapper  
asSequence android.content.ContextWrapper  cancel android.content.ContextWrapper  chatHistoryCallback android.content.ContextWrapper  contains android.content.ContextWrapper  contentResolver android.content.ContextWrapper  delay android.content.ContextWrapper  enableEdgeToEdge android.content.ContextWrapper  equals android.content.ContextWrapper  extractChatHistory android.content.ContextWrapper  fillMaxSize android.content.ContextWrapper  findNodeByClassName android.content.ContextWrapper  findNodeByText android.content.ContextWrapper  findScrollableNode android.content.ContextWrapper  floatingView android.content.ContextWrapper  forEach android.content.ContextWrapper  
forceStart android.content.ContextWrapper  generateHtml android.content.ContextWrapper  getInstance android.content.ContextWrapper  getLocalIpAddress android.content.ContextWrapper  getSystemService android.content.ContextWrapper  getValue android.content.ContextWrapper  handleCustomerServicePage android.content.ContextWrapper  indexOf android.content.ContextWrapper  instance android.content.ContextWrapper  isAccessibilityServiceEnabled android.content.ContextWrapper  isAutomationRunning android.content.ContextWrapper  isBlank android.content.ContextWrapper  isExtractionRunning android.content.ContextWrapper  
isInitialized android.content.ContextWrapper  isMonitoringMode android.content.ContextWrapper  java android.content.ContextWrapper  launch android.content.ContextWrapper  let android.content.ContextWrapper  listOf android.content.ContextWrapper  matches android.content.ContextWrapper  
mutableListOf android.content.ContextWrapper  mutableStateOf android.content.ContextWrapper  newFixedLengthResponse android.content.ContextWrapper  packageName android.content.ContextWrapper  padding android.content.ContextWrapper  provideDelegate android.content.ContextWrapper  repeat android.content.ContextWrapper  replace android.content.ContextWrapper  	resources android.content.ContextWrapper  rootInActiveWindow android.content.ContextWrapper  set android.content.ContextWrapper  
setContent android.content.ContextWrapper  setValue android.content.ContextWrapper  sortedBy android.content.ContextWrapper  
startActivity android.content.ContextWrapper  startForegroundService android.content.ContextWrapper  startService android.content.ContextWrapper  
startsWith android.content.ContextWrapper  substringAfterLast android.content.ContextWrapper  until android.content.ContextWrapper  withContext android.content.ContextWrapper  FLAG_ACTIVITY_NEW_TASK android.content.Intent  action android.content.Intent  flags android.content.Intent  displayMetrics android.content.res.Resources  AccessibilityNodeInfo android.graphics  AccessibilityService android.graphics  Boolean android.graphics  CLICK_MARKER_DURATION android.graphics  CONVERSATION_LIST_XPATH android.graphics  Color android.graphics  Context android.graphics  	Exception android.graphics  GestureDescription android.graphics  Gravity android.graphics  Handler android.graphics  Int android.graphics  List android.graphics  Log android.graphics  Looper android.graphics  MutableList android.graphics  Path android.graphics  PixelFormat android.graphics  Rect android.graphics  Regex android.graphics  String android.graphics  TAG android.graphics  TARGET_PACKAGE android.graphics  View android.graphics  
WindowManager android.graphics  apply android.graphics  contains android.graphics  equals android.graphics  filter android.graphics  forEach android.graphics  getValue android.graphics  
isNotEmpty android.graphics  
isNullOrEmpty android.graphics  lazy android.graphics  let android.graphics  matches android.graphics  
mutableListOf android.graphics  provideDelegate android.graphics  split android.graphics  substringAfterLast android.graphics  toInt android.graphics  toIntOrNull android.graphics  trim android.graphics  until android.graphics  	withIndex android.graphics  GestureResultCallback %android.graphics.AccessibilityService  RED android.graphics.Color  apply android.graphics.Path  moveTo android.graphics.Path  TRANSLUCENT android.graphics.PixelFormat  centerX android.graphics.Rect  centerY android.graphics.Rect  height android.graphics.Rect  isEmpty android.graphics.Rect  left android.graphics.Rect  right android.graphics.Rect  top android.graphics.Rect  width android.graphics.Rect  Uri android.net  parse android.net.Uri  Build 
android.os  Bundle 
android.os  Handler 
android.os  IBinder 
android.os  Looper 
android.os  SDK_INT android.os.Build.VERSION  O android.os.Build.VERSION_CODES  S android.os.Build.VERSION_CODES  putCharSequence android.os.Bundle  postDelayed android.os.Handler  removeCallbacks android.os.Handler  
getMainLooper android.os.Looper  Settings android.provider  ACTION_ACCESSIBILITY_SETTINGS android.provider.Settings   ACTION_MANAGE_OVERLAY_PERMISSION android.provider.Settings  canDrawOverlays android.provider.Settings  ENABLED_ACCESSIBILITY_SERVICES  android.provider.Settings.Secure  	getString  android.provider.Settings.Secure  	TextUtils android.text  SimpleStringSplitter android.text.TextUtils  hasNext +android.text.TextUtils.SimpleStringSplitter  next +android.text.TextUtils.SimpleStringSplitter  	setString +android.text.TextUtils.SimpleStringSplitter  Log android.util  density android.util.DisplayMetrics  heightPixels android.util.DisplayMetrics  widthPixels android.util.DisplayMetrics  d android.util.Log  e android.util.Log  i android.util.Log  w android.util.Log  Gravity android.view  LayoutInflater android.view  MotionEvent android.view  View android.view  	ViewGroup android.view  ViewPropertyAnimator android.view  
WindowManager android.view  ActivityResultContracts  android.view.ContextThemeWrapper  Build  android.view.ContextThemeWrapper  ChatbotAccessibilityService  android.view.ContextThemeWrapper  ChatbotTheme  android.view.ContextThemeWrapper  	Exception  android.view.ContextThemeWrapper  FloatingWindowService  android.view.ContextThemeWrapper  Intent  android.view.ContextThemeWrapper  Log  android.view.ContextThemeWrapper  MainContent  android.view.ContextThemeWrapper  Modifier  android.view.ContextThemeWrapper  Scaffold  android.view.ContextThemeWrapper  Settings  android.view.ContextThemeWrapper  	TextUtils  android.view.ContextThemeWrapper  Toast  android.view.ContextThemeWrapper  Uri  android.view.ContextThemeWrapper  WebViewService  android.view.ContextThemeWrapper  enableEdgeToEdge  android.view.ContextThemeWrapper  equals  android.view.ContextThemeWrapper  fillMaxSize  android.view.ContextThemeWrapper  getLocalIpAddress  android.view.ContextThemeWrapper  getValue  android.view.ContextThemeWrapper  java  android.view.ContextThemeWrapper  mutableStateOf  android.view.ContextThemeWrapper  padding  android.view.ContextThemeWrapper  provideDelegate  android.view.ContextThemeWrapper  
setContent  android.view.ContextThemeWrapper  setValue  android.view.ContextThemeWrapper  START android.view.Gravity  TOP android.view.Gravity  from android.view.LayoutInflater  inflate android.view.LayoutInflater  ACTION_DOWN android.view.MotionEvent  ACTION_MOVE android.view.MotionEvent  	ACTION_UP android.view.MotionEvent  action android.view.MotionEvent  rawX android.view.MotionEvent  rawY android.view.MotionEvent  Color android.view.View  OnClickListener android.view.View  OnTouchListener android.view.View  alpha android.view.View  animate android.view.View  apply android.view.View  findViewById android.view.View  isAttachedToWindow android.view.View  let android.view.View  performClick android.view.View  setBackgroundColor android.view.View  setOnClickListener android.view.View  setOnTouchListener android.view.View  <SAM-CONSTRUCTOR> !android.view.View.OnClickListener  <SAM-CONSTRUCTOR> !android.view.View.OnTouchListener  WRAP_CONTENT #android.view.ViewGroup.LayoutParams  height #android.view.ViewGroup.LayoutParams  width #android.view.ViewGroup.LayoutParams  addView android.view.ViewManager  
removeView android.view.ViewManager  updateViewLayout android.view.ViewManager  alpha !android.view.ViewPropertyAnimator  setDuration !android.view.ViewPropertyAnimator  start !android.view.ViewPropertyAnimator  
withEndAction !android.view.ViewPropertyAnimator  LayoutParams android.view.WindowManager  addView android.view.WindowManager  
removeView android.view.WindowManager  updateViewLayout android.view.WindowManager  FLAG_LAYOUT_IN_SCREEN 'android.view.WindowManager.LayoutParams  FLAG_NOT_FOCUSABLE 'android.view.WindowManager.LayoutParams  FLAG_NOT_TOUCHABLE 'android.view.WindowManager.LayoutParams  Gravity 'android.view.WindowManager.LayoutParams  PixelFormat 'android.view.WindowManager.LayoutParams  TYPE_ACCESSIBILITY_OVERLAY 'android.view.WindowManager.LayoutParams  TYPE_APPLICATION_OVERLAY 'android.view.WindowManager.LayoutParams  WRAP_CONTENT 'android.view.WindowManager.LayoutParams  
WindowManager 'android.view.WindowManager.LayoutParams  apply 'android.view.WindowManager.LayoutParams  flags 'android.view.WindowManager.LayoutParams  format 'android.view.WindowManager.LayoutParams  gravity 'android.view.WindowManager.LayoutParams  height 'android.view.WindowManager.LayoutParams  	resources 'android.view.WindowManager.LayoutParams  type 'android.view.WindowManager.LayoutParams  width 'android.view.WindowManager.LayoutParams  x 'android.view.WindowManager.LayoutParams  y 'android.view.WindowManager.LayoutParams  AccessibilityEvent android.view.accessibility  AccessibilityManager android.view.accessibility  AccessibilityNodeInfo android.view.accessibility  TYPE_VIEW_CLICKED -android.view.accessibility.AccessibilityEvent  TYPE_VIEW_FOCUSED -android.view.accessibility.AccessibilityEvent  TYPE_VIEW_SCROLLED -android.view.accessibility.AccessibilityEvent  TYPE_WINDOW_CONTENT_CHANGED -android.view.accessibility.AccessibilityEvent  TYPE_WINDOW_STATE_CHANGED -android.view.accessibility.AccessibilityEvent  	eventType -android.view.accessibility.AccessibilityEvent  packageName -android.view.accessibility.AccessibilityEvent  source -android.view.accessibility.AccessibilityEvent  %ACTION_ARGUMENT_SET_TEXT_CHARSEQUENCE 0android.view.accessibility.AccessibilityNodeInfo  ACTION_CLICK 0android.view.accessibility.AccessibilityNodeInfo  ACTION_SCROLL_BACKWARD 0android.view.accessibility.AccessibilityNodeInfo  ACTION_SET_TEXT 0android.view.accessibility.AccessibilityNodeInfo  AccessibilityNodeInfo 0android.view.accessibility.AccessibilityNodeInfo  Axis 0android.view.accessibility.AccessibilityNodeInfo  
childCount 0android.view.accessibility.AccessibilityNodeInfo  	className 0android.view.accessibility.AccessibilityNodeInfo  contentDescription 0android.view.accessibility.AccessibilityNodeInfo  endsWith 0android.view.accessibility.AccessibilityNodeInfo  
filterIndexed 0android.view.accessibility.AccessibilityNodeInfo  	filterNot 0android.view.accessibility.AccessibilityNodeInfo  findAllDescendants 0android.view.accessibility.AccessibilityNodeInfo  findNodeByXPath 0android.view.accessibility.AccessibilityNodeInfo  findNodesByXPath 0android.view.accessibility.AccessibilityNodeInfo  firstOrNull 0android.view.accessibility.AccessibilityNodeInfo  getBoundsInScreen 0android.view.accessibility.AccessibilityNodeInfo  getChild 0android.view.accessibility.AccessibilityNodeInfo  	isChecked 0android.view.accessibility.AccessibilityNodeInfo  isClickable 0android.view.accessibility.AccessibilityNodeInfo  isEmpty 0android.view.accessibility.AccessibilityNodeInfo  	isEnabled 0android.view.accessibility.AccessibilityNodeInfo  	isFocused 0android.view.accessibility.AccessibilityNodeInfo  
isNotEmpty 0android.view.accessibility.AccessibilityNodeInfo  isScrollable 0android.view.accessibility.AccessibilityNodeInfo  
isSelected 0android.view.accessibility.AccessibilityNodeInfo  isVisibleToUser 0android.view.accessibility.AccessibilityNodeInfo  let 0android.view.accessibility.AccessibilityNodeInfo  listOf 0android.view.accessibility.AccessibilityNodeInfo  
listOfNotNull 0android.view.accessibility.AccessibilityNodeInfo  
mapNotNull 0android.view.accessibility.AccessibilityNodeInfo  matchesPredicates 0android.view.accessibility.AccessibilityNodeInfo  mutableSetOf 0android.view.accessibility.AccessibilityNodeInfo  packageName 0android.view.accessibility.AccessibilityNodeInfo  parent 0android.view.accessibility.AccessibilityNodeInfo  parseSegment 0android.view.accessibility.AccessibilityNodeInfo  
performAction 0android.view.accessibility.AccessibilityNodeInfo  recycle 0android.view.accessibility.AccessibilityNodeInfo  setOf 0android.view.accessibility.AccessibilityNodeInfo  text 0android.view.accessibility.AccessibilityNodeInfo  toList 0android.view.accessibility.AccessibilityNodeInfo  tokenize 0android.view.accessibility.AccessibilityNodeInfo  until 0android.view.accessibility.AccessibilityNodeInfo  viewIdResourceName 0android.view.accessibility.AccessibilityNodeInfo  source .android.view.accessibility.AccessibilityRecord  ImageButton android.widget  Toast android.widget  setImageResource android.widget.ImageView  LENGTH_LONG android.widget.Toast  LENGTH_SHORT android.widget.Toast  makeText android.widget.Toast  show android.widget.Toast  ComponentActivity androidx.activity  enableEdgeToEdge androidx.activity  ActivityResultContracts #androidx.activity.ComponentActivity  Build #androidx.activity.ComponentActivity  ChatbotAccessibilityService #androidx.activity.ComponentActivity  ChatbotTheme #androidx.activity.ComponentActivity  	Exception #androidx.activity.ComponentActivity  FloatingWindowService #androidx.activity.ComponentActivity  Intent #androidx.activity.ComponentActivity  Log #androidx.activity.ComponentActivity  MainContent #androidx.activity.ComponentActivity  Modifier #androidx.activity.ComponentActivity  Scaffold #androidx.activity.ComponentActivity  Settings #androidx.activity.ComponentActivity  	TextUtils #androidx.activity.ComponentActivity  Toast #androidx.activity.ComponentActivity  Uri #androidx.activity.ComponentActivity  WebViewService #androidx.activity.ComponentActivity  enableEdgeToEdge #androidx.activity.ComponentActivity  equals #androidx.activity.ComponentActivity  fillMaxSize #androidx.activity.ComponentActivity  getLocalIpAddress #androidx.activity.ComponentActivity  getValue #androidx.activity.ComponentActivity  java #androidx.activity.ComponentActivity  mutableStateOf #androidx.activity.ComponentActivity  onCreate #androidx.activity.ComponentActivity  onResume #androidx.activity.ComponentActivity  padding #androidx.activity.ComponentActivity  provideDelegate #androidx.activity.ComponentActivity  registerForActivityResult #androidx.activity.ComponentActivity  
setContent #androidx.activity.ComponentActivity  setValue #androidx.activity.ComponentActivity  
setContent androidx.activity.compose  ActivityResultCallback androidx.activity.result  ActivityResultLauncher androidx.activity.result  <SAM-CONSTRUCTOR> /androidx.activity.result.ActivityResultCallback  launch /androidx.activity.result.ActivityResultLauncher  ActivityResultContracts !androidx.activity.result.contract  StartActivityForResult 9androidx.activity.result.contract.ActivityResultContracts  isSystemInDarkTheme androidx.compose.foundation  ActivityResultContracts "androidx.compose.foundation.layout  	Alignment "androidx.compose.foundation.layout  Arrangement "androidx.compose.foundation.layout  Boolean "androidx.compose.foundation.layout  Build "androidx.compose.foundation.layout  Bundle "androidx.compose.foundation.layout  Button "androidx.compose.foundation.layout  ButtonDefaults "androidx.compose.foundation.layout  ChatbotAccessibilityService "androidx.compose.foundation.layout  ChatbotTheme "androidx.compose.foundation.layout  Column "androidx.compose.foundation.layout  ColumnScope "androidx.compose.foundation.layout  ComponentActivity "androidx.compose.foundation.layout  
Composable "androidx.compose.foundation.layout  	Exception "androidx.compose.foundation.layout  FloatingWindowService "androidx.compose.foundation.layout  Intent "androidx.compose.foundation.layout  Log "androidx.compose.foundation.layout  MainContent "androidx.compose.foundation.layout  
MaterialTheme "androidx.compose.foundation.layout  Modifier "androidx.compose.foundation.layout  
PaddingValues "androidx.compose.foundation.layout  RowScope "androidx.compose.foundation.layout  Scaffold "androidx.compose.foundation.layout  Settings "androidx.compose.foundation.layout  Spacer "androidx.compose.foundation.layout  String "androidx.compose.foundation.layout  Text "androidx.compose.foundation.layout  	TextAlign "androidx.compose.foundation.layout  	TextUtils "androidx.compose.foundation.layout  Toast "androidx.compose.foundation.layout  Unit "androidx.compose.foundation.layout  Uri "androidx.compose.foundation.layout  WebViewService "androidx.compose.foundation.layout  buttonColors "androidx.compose.foundation.layout  equals "androidx.compose.foundation.layout  fillMaxSize "androidx.compose.foundation.layout  getLocalIpAddress "androidx.compose.foundation.layout  getValue "androidx.compose.foundation.layout  height "androidx.compose.foundation.layout  
isNotEmpty "androidx.compose.foundation.layout  java "androidx.compose.foundation.layout  mutableStateOf "androidx.compose.foundation.layout  padding "androidx.compose.foundation.layout  provideDelegate "androidx.compose.foundation.layout  setValue "androidx.compose.foundation.layout  Center .androidx.compose.foundation.layout.Arrangement  HorizontalOrVertical .androidx.compose.foundation.layout.Arrangement  Button .androidx.compose.foundation.layout.ColumnScope  ButtonDefaults .androidx.compose.foundation.layout.ColumnScope  
MaterialTheme .androidx.compose.foundation.layout.ColumnScope  Modifier .androidx.compose.foundation.layout.ColumnScope  Spacer .androidx.compose.foundation.layout.ColumnScope  Text .androidx.compose.foundation.layout.ColumnScope  	TextAlign .androidx.compose.foundation.layout.ColumnScope  buttonColors .androidx.compose.foundation.layout.ColumnScope  dp .androidx.compose.foundation.layout.ColumnScope  height .androidx.compose.foundation.layout.ColumnScope  
isNotEmpty .androidx.compose.foundation.layout.ColumnScope  sp .androidx.compose.foundation.layout.ColumnScope  Text +androidx.compose.foundation.layout.RowScope  ActivityResultContracts androidx.compose.material3  	Alignment androidx.compose.material3  Arrangement androidx.compose.material3  Boolean androidx.compose.material3  Build androidx.compose.material3  Bundle androidx.compose.material3  Button androidx.compose.material3  ButtonColors androidx.compose.material3  ButtonDefaults androidx.compose.material3  ChatbotAccessibilityService androidx.compose.material3  ChatbotTheme androidx.compose.material3  ColorScheme androidx.compose.material3  Column androidx.compose.material3  ComponentActivity androidx.compose.material3  
Composable androidx.compose.material3  	Exception androidx.compose.material3  FloatingWindowService androidx.compose.material3  Intent androidx.compose.material3  Log androidx.compose.material3  MainContent androidx.compose.material3  
MaterialTheme androidx.compose.material3  Modifier androidx.compose.material3  Scaffold androidx.compose.material3  Settings androidx.compose.material3  Spacer androidx.compose.material3  String androidx.compose.material3  Text androidx.compose.material3  	TextAlign androidx.compose.material3  	TextUtils androidx.compose.material3  Toast androidx.compose.material3  
Typography androidx.compose.material3  Unit androidx.compose.material3  Uri androidx.compose.material3  WebViewService androidx.compose.material3  buttonColors androidx.compose.material3  darkColorScheme androidx.compose.material3  dynamicDarkColorScheme androidx.compose.material3  dynamicLightColorScheme androidx.compose.material3  equals androidx.compose.material3  fillMaxSize androidx.compose.material3  getLocalIpAddress androidx.compose.material3  getValue androidx.compose.material3  height androidx.compose.material3  
isNotEmpty androidx.compose.material3  java androidx.compose.material3  lightColorScheme androidx.compose.material3  mutableStateOf androidx.compose.material3  padding androidx.compose.material3  provideDelegate androidx.compose.material3  setValue androidx.compose.material3  buttonColors )androidx.compose.material3.ButtonDefaults  error &androidx.compose.material3.ColorScheme  onSurfaceVariant &androidx.compose.material3.ColorScheme  primary &androidx.compose.material3.ColorScheme  tertiary &androidx.compose.material3.ColorScheme  colorScheme (androidx.compose.material3.MaterialTheme  ActivityResultContracts androidx.compose.runtime  	Alignment androidx.compose.runtime  Arrangement androidx.compose.runtime  Boolean androidx.compose.runtime  Build androidx.compose.runtime  Bundle androidx.compose.runtime  Button androidx.compose.runtime  ButtonDefaults androidx.compose.runtime  ChatbotAccessibilityService androidx.compose.runtime  ChatbotTheme androidx.compose.runtime  Column androidx.compose.runtime  ComponentActivity androidx.compose.runtime  
Composable androidx.compose.runtime  	Exception androidx.compose.runtime  FloatingWindowService androidx.compose.runtime  Intent androidx.compose.runtime  Log androidx.compose.runtime  MainContent androidx.compose.runtime  
MaterialTheme androidx.compose.runtime  Modifier androidx.compose.runtime  MutableState androidx.compose.runtime  ProvidableCompositionLocal androidx.compose.runtime  Scaffold androidx.compose.runtime  Settings androidx.compose.runtime  Spacer androidx.compose.runtime  String androidx.compose.runtime  Text androidx.compose.runtime  	TextAlign androidx.compose.runtime  	TextUtils androidx.compose.runtime  Toast androidx.compose.runtime  Unit androidx.compose.runtime  Uri androidx.compose.runtime  WebViewService androidx.compose.runtime  buttonColors androidx.compose.runtime  equals androidx.compose.runtime  fillMaxSize androidx.compose.runtime  getLocalIpAddress androidx.compose.runtime  getValue androidx.compose.runtime  height androidx.compose.runtime  
isNotEmpty androidx.compose.runtime  java androidx.compose.runtime  mutableStateOf androidx.compose.runtime  padding androidx.compose.runtime  provideDelegate androidx.compose.runtime  setValue androidx.compose.runtime  
getCurrent )androidx.compose.runtime.CompositionLocal  getValue %androidx.compose.runtime.MutableState  provideDelegate %androidx.compose.runtime.MutableState  setValue %androidx.compose.runtime.MutableState  current 3androidx.compose.runtime.ProvidableCompositionLocal  ComposableFunction0 !androidx.compose.runtime.internal  ComposableFunction1 !androidx.compose.runtime.internal  	Alignment androidx.compose.ui  Modifier androidx.compose.ui  CenterHorizontally androidx.compose.ui.Alignment  	Companion androidx.compose.ui.Alignment  
Horizontal androidx.compose.ui.Alignment  CenterHorizontally 'androidx.compose.ui.Alignment.Companion  	Companion androidx.compose.ui.Modifier  fillMaxSize androidx.compose.ui.Modifier  height androidx.compose.ui.Modifier  padding androidx.compose.ui.Modifier  fillMaxSize &androidx.compose.ui.Modifier.Companion  height &androidx.compose.ui.Modifier.Companion  padding &androidx.compose.ui.Modifier.Companion  Color androidx.compose.ui.graphics  LocalContext androidx.compose.ui.platform  	TextStyle androidx.compose.ui.text  
FontFamily androidx.compose.ui.text.font  
FontWeight androidx.compose.ui.text.font  SystemFontFamily androidx.compose.ui.text.font  	Companion (androidx.compose.ui.text.font.FontFamily  Default (androidx.compose.ui.text.font.FontFamily  Default 2androidx.compose.ui.text.font.FontFamily.Companion  	Companion (androidx.compose.ui.text.font.FontWeight  Normal (androidx.compose.ui.text.font.FontWeight  Normal 2androidx.compose.ui.text.font.FontWeight.Companion  	TextAlign androidx.compose.ui.text.style  Center (androidx.compose.ui.text.style.TextAlign  	Companion (androidx.compose.ui.text.style.TextAlign  Center 2androidx.compose.ui.text.style.TextAlign.Companion  Dp androidx.compose.ui.unit  TextUnit androidx.compose.ui.unit  dp androidx.compose.ui.unit  getDp androidx.compose.ui.unit  sp androidx.compose.ui.unit  NotificationCompat androidx.core.app  ActivityResultContracts #androidx.core.app.ComponentActivity  Boolean #androidx.core.app.ComponentActivity  Build #androidx.core.app.ComponentActivity  Bundle #androidx.core.app.ComponentActivity  ChatbotAccessibilityService #androidx.core.app.ComponentActivity  ChatbotTheme #androidx.core.app.ComponentActivity  	Exception #androidx.core.app.ComponentActivity  FloatingWindowService #androidx.core.app.ComponentActivity  Intent #androidx.core.app.ComponentActivity  Log #androidx.core.app.ComponentActivity  MainContent #androidx.core.app.ComponentActivity  Modifier #androidx.core.app.ComponentActivity  Scaffold #androidx.core.app.ComponentActivity  Settings #androidx.core.app.ComponentActivity  	TextUtils #androidx.core.app.ComponentActivity  Toast #androidx.core.app.ComponentActivity  Uri #androidx.core.app.ComponentActivity  WebViewService #androidx.core.app.ComponentActivity  enableEdgeToEdge #androidx.core.app.ComponentActivity  equals #androidx.core.app.ComponentActivity  fillMaxSize #androidx.core.app.ComponentActivity  getLocalIpAddress #androidx.core.app.ComponentActivity  getValue #androidx.core.app.ComponentActivity  java #androidx.core.app.ComponentActivity  mutableStateOf #androidx.core.app.ComponentActivity  padding #androidx.core.app.ComponentActivity  provideDelegate #androidx.core.app.ComponentActivity  
setContent #androidx.core.app.ComponentActivity  setValue #androidx.core.app.ComponentActivity  Builder $androidx.core.app.NotificationCompat  PRIORITY_LOW $androidx.core.app.NotificationCompat  build ,androidx.core.app.NotificationCompat.Builder  setContentText ,androidx.core.app.NotificationCompat.Builder  setContentTitle ,androidx.core.app.NotificationCompat.Builder  setPriority ,androidx.core.app.NotificationCompat.Builder  setSmallIcon ,androidx.core.app.NotificationCompat.Builder  AccessibilityNodeInfoCompat  androidx.core.view.accessibility  ActivityResultContracts com.coffee.chatbot  	Alignment com.coffee.chatbot  Arrangement com.coffee.chatbot  Boolean com.coffee.chatbot  Build com.coffee.chatbot  Bundle com.coffee.chatbot  Button com.coffee.chatbot  ButtonDefaults com.coffee.chatbot  ChatbotAccessibilityService com.coffee.chatbot  ChatbotTheme com.coffee.chatbot  Column com.coffee.chatbot  ComponentActivity com.coffee.chatbot  
Composable com.coffee.chatbot  	Exception com.coffee.chatbot  FloatingWindowService com.coffee.chatbot  Intent com.coffee.chatbot  Log com.coffee.chatbot  MainActivity com.coffee.chatbot  MainContent com.coffee.chatbot  
MaterialTheme com.coffee.chatbot  Modifier com.coffee.chatbot  R com.coffee.chatbot  Scaffold com.coffee.chatbot  Settings com.coffee.chatbot  Spacer com.coffee.chatbot  String com.coffee.chatbot  Text com.coffee.chatbot  	TextAlign com.coffee.chatbot  	TextUtils com.coffee.chatbot  Toast com.coffee.chatbot  Unit com.coffee.chatbot  Uri com.coffee.chatbot  WebViewService com.coffee.chatbot  buttonColors com.coffee.chatbot  equals com.coffee.chatbot  fillMaxSize com.coffee.chatbot  getLocalIpAddress com.coffee.chatbot  getValue com.coffee.chatbot  height com.coffee.chatbot  
isNotEmpty com.coffee.chatbot  java com.coffee.chatbot  mutableStateOf com.coffee.chatbot  padding com.coffee.chatbot  provideDelegate com.coffee.chatbot  setValue com.coffee.chatbot  ActivityResultContracts com.coffee.chatbot.MainActivity  Build com.coffee.chatbot.MainActivity  ChatbotAccessibilityService com.coffee.chatbot.MainActivity  ChatbotTheme com.coffee.chatbot.MainActivity  FloatingWindowService com.coffee.chatbot.MainActivity  Intent com.coffee.chatbot.MainActivity  Log com.coffee.chatbot.MainActivity  MainContent com.coffee.chatbot.MainActivity  Modifier com.coffee.chatbot.MainActivity  Scaffold com.coffee.chatbot.MainActivity  Settings com.coffee.chatbot.MainActivity  	TextUtils com.coffee.chatbot.MainActivity  Toast com.coffee.chatbot.MainActivity  Uri com.coffee.chatbot.MainActivity  WebViewService com.coffee.chatbot.MainActivity  checkAccessibilityPermission com.coffee.chatbot.MainActivity  checkAndRequestPermissions com.coffee.chatbot.MainActivity  contentResolver com.coffee.chatbot.MainActivity  enableEdgeToEdge com.coffee.chatbot.MainActivity  equals com.coffee.chatbot.MainActivity  fillMaxSize com.coffee.chatbot.MainActivity  getLocalIpAddress com.coffee.chatbot.MainActivity  getValue com.coffee.chatbot.MainActivity  isAccessibilityServiceEnabled com.coffee.chatbot.MainActivity  isFloatingServiceRunning com.coffee.chatbot.MainActivity  isServiceRunning com.coffee.chatbot.MainActivity  java com.coffee.chatbot.MainActivity  mutableStateOf com.coffee.chatbot.MainActivity  overlayPermissionLauncher com.coffee.chatbot.MainActivity  packageName com.coffee.chatbot.MainActivity  padding com.coffee.chatbot.MainActivity  provideDelegate com.coffee.chatbot.MainActivity  registerForActivityResult com.coffee.chatbot.MainActivity  requestAccessibilityPermission com.coffee.chatbot.MainActivity  requestOverlayPermission com.coffee.chatbot.MainActivity  
setContent com.coffee.chatbot.MainActivity  setValue com.coffee.chatbot.MainActivity  
startActivity com.coffee.chatbot.MainActivity  startForegroundService com.coffee.chatbot.MainActivity  startService com.coffee.chatbot.MainActivity  
startServices com.coffee.chatbot.MainActivity  stopServices com.coffee.chatbot.MainActivity  updateUI com.coffee.chatbot.MainActivity  updateWebServerUrl com.coffee.chatbot.MainActivity  webServerUrl com.coffee.chatbot.MainActivity  ic_launcher_foreground com.coffee.chatbot.R.drawable  floating_button com.coffee.chatbot.R.id  floating_button_layout com.coffee.chatbot.R.layout  ChatMessage com.coffee.chatbot.model  Long com.coffee.chatbot.model  Rect com.coffee.chatbot.model  String com.coffee.chatbot.model  System com.coffee.chatbot.model  bounds $com.coffee.chatbot.model.ChatMessage  AccessibilityEvent com.coffee.chatbot.service  AccessibilityNodeInfo com.coffee.chatbot.service  AccessibilityService com.coffee.chatbot.service  AtomicReference com.coffee.chatbot.service  Axis com.coffee.chatbot.service  Boolean com.coffee.chatbot.service  Build com.coffee.chatbot.service  Bundle com.coffee.chatbot.service  
CHANNEL_ID com.coffee.chatbot.service  CLICK_MARKER_DURATION com.coffee.chatbot.service  CONVERSATION_LIST_XPATH com.coffee.chatbot.service  ChatMessage com.coffee.chatbot.service  ChatbotAccessibilityService com.coffee.chatbot.service  Color com.coffee.chatbot.service  ConcurrentHashMap com.coffee.chatbot.service  Context com.coffee.chatbot.service  CoroutineScope com.coffee.chatbot.service  CustomerServiceHandler com.coffee.chatbot.service  DEFAULT_PORT com.coffee.chatbot.service  Dispatchers com.coffee.chatbot.service  	Exception com.coffee.chatbot.service  FloatingWindowService com.coffee.chatbot.service  GLOBAL_ACTION_BACK com.coffee.chatbot.service  GestureDescription com.coffee.chatbot.service  Gravity com.coffee.chatbot.service  Handler com.coffee.chatbot.service  IBinder com.coffee.chatbot.service  IHTTPSession com.coffee.chatbot.service  IOException com.coffee.chatbot.service  IllegalArgumentException com.coffee.chatbot.service  ImageButton com.coffee.chatbot.service  Int com.coffee.chatbot.service  Intent com.coffee.chatbot.service  LayoutInflater com.coffee.chatbot.service  List com.coffee.chatbot.service  Log com.coffee.chatbot.service  Looper com.coffee.chatbot.service  Math com.coffee.chatbot.service  MessageGroup com.coffee.chatbot.service  MotionEvent com.coffee.chatbot.service  MutableList com.coffee.chatbot.service  NOTIFICATION_ID com.coffee.chatbot.service  	NanoHTTPD com.coffee.chatbot.service  NetworkInterface com.coffee.chatbot.service  NodeHierarchyServer com.coffee.chatbot.service  Notification com.coffee.chatbot.service  NotificationChannel com.coffee.chatbot.service  NotificationCompat com.coffee.chatbot.service  NotificationManager com.coffee.chatbot.service  Pair com.coffee.chatbot.service  Path com.coffee.chatbot.service  PathStep com.coffee.chatbot.service  Pattern com.coffee.chatbot.service  PixelFormat com.coffee.chatbot.service  R com.coffee.chatbot.service  Rect com.coffee.chatbot.service  Regex com.coffee.chatbot.service  Response com.coffee.chatbot.service  Runnable com.coffee.chatbot.service  START_NOT_STICKY com.coffee.chatbot.service  START_STICKY com.coffee.chatbot.service  Service com.coffee.chatbot.service  Settings com.coffee.chatbot.service  String com.coffee.chatbot.service  
StringBuilder com.coffee.chatbot.service  
SupervisorJob com.coffee.chatbot.service  TAG com.coffee.chatbot.service  TARGET_PACKAGE com.coffee.chatbot.service  	TextUtils com.coffee.chatbot.service  Toast com.coffee.chatbot.service  Unit com.coffee.chatbot.service  View com.coffee.chatbot.service  Volatile com.coffee.chatbot.service  WebViewService com.coffee.chatbot.service  
WindowManager com.coffee.chatbot.service  all com.coffee.chatbot.service  android com.coffee.chatbot.service  any com.coffee.chatbot.service  apply com.coffee.chatbot.service  
asSequence com.coffee.chatbot.service  cancel com.coffee.chatbot.service  chatHistoryCallback com.coffee.chatbot.service  clickByXPath com.coffee.chatbot.service  contains com.coffee.chatbot.service  delay com.coffee.chatbot.service  	emptyList com.coffee.chatbot.service  endsWith com.coffee.chatbot.service  equals com.coffee.chatbot.service  evaluateLastPredicate com.coffee.chatbot.service  evaluatePositionPredicate com.coffee.chatbot.service  
existsByXPath com.coffee.chatbot.service  extractChatHistory com.coffee.chatbot.service  filter com.coffee.chatbot.service  
filterIndexed com.coffee.chatbot.service  	filterNot com.coffee.chatbot.service  findAllDescendants com.coffee.chatbot.service  findNodeByClassName com.coffee.chatbot.service  findNodeByText com.coffee.chatbot.service  findNodeByXPath com.coffee.chatbot.service  findNodesByXPath com.coffee.chatbot.service  findScrollableNode com.coffee.chatbot.service  firstOrNull com.coffee.chatbot.service  floatingView com.coffee.chatbot.service  forEach com.coffee.chatbot.service  
forceStart com.coffee.chatbot.service  generateHtml com.coffee.chatbot.service  getInstance com.coffee.chatbot.service  getLocalIpAddress com.coffee.chatbot.service  getValue com.coffee.chatbot.service  handleCustomerServicePage com.coffee.chatbot.service  indexOf com.coffee.chatbot.service  instance com.coffee.chatbot.service  isAccessibilityServiceEnabled com.coffee.chatbot.service  isAutomationRunning com.coffee.chatbot.service  isBlank com.coffee.chatbot.service  isEmpty com.coffee.chatbot.service  isExtractionRunning com.coffee.chatbot.service  
isInitialized com.coffee.chatbot.service  isMonitoringMode com.coffee.chatbot.service  
isNotEmpty com.coffee.chatbot.service  
isNullOrEmpty com.coffee.chatbot.service  java com.coffee.chatbot.service  launch com.coffee.chatbot.service  lazy com.coffee.chatbot.service  let com.coffee.chatbot.service  listOf com.coffee.chatbot.service  
listOfNotNull com.coffee.chatbot.service  	lowercase com.coffee.chatbot.service  
mapNotNull com.coffee.chatbot.service  matches com.coffee.chatbot.service  matchesAttribute com.coffee.chatbot.service  matchesPredicates com.coffee.chatbot.service  
mutableListOf com.coffee.chatbot.service  mutableSetOf com.coffee.chatbot.service  newFixedLengthResponse com.coffee.chatbot.service  parseSegment com.coffee.chatbot.service  provideDelegate com.coffee.chatbot.service  repeat com.coffee.chatbot.service  replace com.coffee.chatbot.service  	resources com.coffee.chatbot.service  rootInActiveWindow com.coffee.chatbot.service  set com.coffee.chatbot.service  setOf com.coffee.chatbot.service  sortedBy com.coffee.chatbot.service  split com.coffee.chatbot.service  
startActivity com.coffee.chatbot.service  
startsWith com.coffee.chatbot.service  	substring com.coffee.chatbot.service  substringAfter com.coffee.chatbot.service  substringAfterLast com.coffee.chatbot.service  toInt com.coffee.chatbot.service  toIntOrNull com.coffee.chatbot.service  toList com.coffee.chatbot.service  toRegex com.coffee.chatbot.service  tokenize com.coffee.chatbot.service  trim com.coffee.chatbot.service  until com.coffee.chatbot.service  withContext com.coffee.chatbot.service  	withIndex com.coffee.chatbot.service  GestureResultCallback /com.coffee.chatbot.service.AccessibilityService  CHILD com.coffee.chatbot.service.Axis  
DESCENDANT com.coffee.chatbot.service.Axis  PARENT com.coffee.chatbot.service.Axis  SELF com.coffee.chatbot.service.Axis  AccessibilityEvent 6com.coffee.chatbot.service.ChatbotAccessibilityService  AccessibilityNodeInfo 6com.coffee.chatbot.service.ChatbotAccessibilityService  Boolean 6com.coffee.chatbot.service.ChatbotAccessibilityService  Bundle 6com.coffee.chatbot.service.ChatbotAccessibilityService  ChatMessage 6com.coffee.chatbot.service.ChatbotAccessibilityService  ChatbotAccessibilityService 6com.coffee.chatbot.service.ChatbotAccessibilityService  	Companion 6com.coffee.chatbot.service.ChatbotAccessibilityService  ConcurrentHashMap 6com.coffee.chatbot.service.ChatbotAccessibilityService  Context 6com.coffee.chatbot.service.ChatbotAccessibilityService  CoroutineScope 6com.coffee.chatbot.service.ChatbotAccessibilityService  CustomerServiceHandler 6com.coffee.chatbot.service.ChatbotAccessibilityService  Dispatchers 6com.coffee.chatbot.service.ChatbotAccessibilityService  	Exception 6com.coffee.chatbot.service.ChatbotAccessibilityService  GLOBAL_ACTION_BACK 6com.coffee.chatbot.service.ChatbotAccessibilityService  Handler 6com.coffee.chatbot.service.ChatbotAccessibilityService  Int 6com.coffee.chatbot.service.ChatbotAccessibilityService  Intent 6com.coffee.chatbot.service.ChatbotAccessibilityService  List 6com.coffee.chatbot.service.ChatbotAccessibilityService  Log 6com.coffee.chatbot.service.ChatbotAccessibilityService  Looper 6com.coffee.chatbot.service.ChatbotAccessibilityService  MessageGroup 6com.coffee.chatbot.service.ChatbotAccessibilityService  MutableList 6com.coffee.chatbot.service.ChatbotAccessibilityService  Rect 6com.coffee.chatbot.service.ChatbotAccessibilityService  Regex 6com.coffee.chatbot.service.ChatbotAccessibilityService  Runnable 6com.coffee.chatbot.service.ChatbotAccessibilityService  Settings 6com.coffee.chatbot.service.ChatbotAccessibilityService  String 6com.coffee.chatbot.service.ChatbotAccessibilityService  
SupervisorJob 6com.coffee.chatbot.service.ChatbotAccessibilityService  TAG 6com.coffee.chatbot.service.ChatbotAccessibilityService  	TextUtils 6com.coffee.chatbot.service.ChatbotAccessibilityService  Unit 6com.coffee.chatbot.service.ChatbotAccessibilityService  Volatile 6com.coffee.chatbot.service.ChatbotAccessibilityService  WebViewService 6com.coffee.chatbot.service.ChatbotAccessibilityService  any 6com.coffee.chatbot.service.ChatbotAccessibilityService  applicationContext 6com.coffee.chatbot.service.ChatbotAccessibilityService  apply 6com.coffee.chatbot.service.ChatbotAccessibilityService  cancel 6com.coffee.chatbot.service.ChatbotAccessibilityService  chatHistoryCallback 6com.coffee.chatbot.service.ChatbotAccessibilityService  checkForNewMessages 6com.coffee.chatbot.service.ChatbotAccessibilityService  contains 6com.coffee.chatbot.service.ChatbotAccessibilityService  coroutineScope 6com.coffee.chatbot.service.ChatbotAccessibilityService  customerServiceHandler 6com.coffee.chatbot.service.ChatbotAccessibilityService  delay 6com.coffee.chatbot.service.ChatbotAccessibilityService  equals 6com.coffee.chatbot.service.ChatbotAccessibilityService  eventTypeToString 6com.coffee.chatbot.service.ChatbotAccessibilityService  extractChatHistory 6com.coffee.chatbot.service.ChatbotAccessibilityService  extractedMessages 6com.coffee.chatbot.service.ChatbotAccessibilityService  findAllNodesOfType 6com.coffee.chatbot.service.ChatbotAccessibilityService  findAllNodesOfTypeRecursive 6com.coffee.chatbot.service.ChatbotAccessibilityService  findNodeByClassName 6com.coffee.chatbot.service.ChatbotAccessibilityService  findNodeByText 6com.coffee.chatbot.service.ChatbotAccessibilityService  findScrollableNode 6com.coffee.chatbot.service.ChatbotAccessibilityService  
forceStart 6com.coffee.chatbot.service.ChatbotAccessibilityService  getInstance 6com.coffee.chatbot.service.ChatbotAccessibilityService  handleCustomerServiceList 6com.coffee.chatbot.service.ChatbotAccessibilityService  handleCustomerServicePage 6com.coffee.chatbot.service.ChatbotAccessibilityService  instance 6com.coffee.chatbot.service.ChatbotAccessibilityService  isAccessibilityServiceEnabled 6com.coffee.chatbot.service.ChatbotAccessibilityService  isBlank 6com.coffee.chatbot.service.ChatbotAccessibilityService  isExtractionRunning 6com.coffee.chatbot.service.ChatbotAccessibilityService  java 6com.coffee.chatbot.service.ChatbotAccessibilityService  launch 6com.coffee.chatbot.service.ChatbotAccessibilityService  listOf 6com.coffee.chatbot.service.ChatbotAccessibilityService  matches 6com.coffee.chatbot.service.ChatbotAccessibilityService  monitoringHandler 6com.coffee.chatbot.service.ChatbotAccessibilityService  monitoringInterval 6com.coffee.chatbot.service.ChatbotAccessibilityService  monitoringRunnable 6com.coffee.chatbot.service.ChatbotAccessibilityService  
mutableListOf 6com.coffee.chatbot.service.ChatbotAccessibilityService  performGlobalAction 6com.coffee.chatbot.service.ChatbotAccessibilityService  	resources 6com.coffee.chatbot.service.ChatbotAccessibilityService  rootInActiveWindow 6com.coffee.chatbot.service.ChatbotAccessibilityService  set 6com.coffee.chatbot.service.ChatbotAccessibilityService  sortedBy 6com.coffee.chatbot.service.ChatbotAccessibilityService  startChatExtraction 6com.coffee.chatbot.service.ChatbotAccessibilityService  startListeningForNewMessages 6com.coffee.chatbot.service.ChatbotAccessibilityService  
startsWith 6com.coffee.chatbot.service.ChatbotAccessibilityService  stopChatExtraction 6com.coffee.chatbot.service.ChatbotAccessibilityService  stopListeningForNewMessages 6com.coffee.chatbot.service.ChatbotAccessibilityService  until 6com.coffee.chatbot.service.ChatbotAccessibilityService  withContext 6com.coffee.chatbot.service.ChatbotAccessibilityService  AccessibilityEvent @com.coffee.chatbot.service.ChatbotAccessibilityService.Companion  AccessibilityNodeInfo @com.coffee.chatbot.service.ChatbotAccessibilityService.Companion  Bundle @com.coffee.chatbot.service.ChatbotAccessibilityService.Companion  ChatMessage @com.coffee.chatbot.service.ChatbotAccessibilityService.Companion  ChatbotAccessibilityService @com.coffee.chatbot.service.ChatbotAccessibilityService.Companion  ConcurrentHashMap @com.coffee.chatbot.service.ChatbotAccessibilityService.Companion  CoroutineScope @com.coffee.chatbot.service.ChatbotAccessibilityService.Companion  CustomerServiceHandler @com.coffee.chatbot.service.ChatbotAccessibilityService.Companion  Dispatchers @com.coffee.chatbot.service.ChatbotAccessibilityService.Companion  GLOBAL_ACTION_BACK @com.coffee.chatbot.service.ChatbotAccessibilityService.Companion  Handler @com.coffee.chatbot.service.ChatbotAccessibilityService.Companion  Intent @com.coffee.chatbot.service.ChatbotAccessibilityService.Companion  Log @com.coffee.chatbot.service.ChatbotAccessibilityService.Companion  Looper @com.coffee.chatbot.service.ChatbotAccessibilityService.Companion  MessageGroup @com.coffee.chatbot.service.ChatbotAccessibilityService.Companion  Rect @com.coffee.chatbot.service.ChatbotAccessibilityService.Companion  Regex @com.coffee.chatbot.service.ChatbotAccessibilityService.Companion  Runnable @com.coffee.chatbot.service.ChatbotAccessibilityService.Companion  Settings @com.coffee.chatbot.service.ChatbotAccessibilityService.Companion  
SupervisorJob @com.coffee.chatbot.service.ChatbotAccessibilityService.Companion  TAG @com.coffee.chatbot.service.ChatbotAccessibilityService.Companion  	TextUtils @com.coffee.chatbot.service.ChatbotAccessibilityService.Companion  WebViewService @com.coffee.chatbot.service.ChatbotAccessibilityService.Companion  any @com.coffee.chatbot.service.ChatbotAccessibilityService.Companion  apply @com.coffee.chatbot.service.ChatbotAccessibilityService.Companion  cancel @com.coffee.chatbot.service.ChatbotAccessibilityService.Companion  chatHistoryCallback @com.coffee.chatbot.service.ChatbotAccessibilityService.Companion  contains @com.coffee.chatbot.service.ChatbotAccessibilityService.Companion  delay @com.coffee.chatbot.service.ChatbotAccessibilityService.Companion  equals @com.coffee.chatbot.service.ChatbotAccessibilityService.Companion  extractChatHistory @com.coffee.chatbot.service.ChatbotAccessibilityService.Companion  findNodeByClassName @com.coffee.chatbot.service.ChatbotAccessibilityService.Companion  findNodeByText @com.coffee.chatbot.service.ChatbotAccessibilityService.Companion  findScrollableNode @com.coffee.chatbot.service.ChatbotAccessibilityService.Companion  
forceStart @com.coffee.chatbot.service.ChatbotAccessibilityService.Companion  getInstance @com.coffee.chatbot.service.ChatbotAccessibilityService.Companion  handleCustomerServicePage @com.coffee.chatbot.service.ChatbotAccessibilityService.Companion  instance @com.coffee.chatbot.service.ChatbotAccessibilityService.Companion  isAccessibilityServiceEnabled @com.coffee.chatbot.service.ChatbotAccessibilityService.Companion  isBlank @com.coffee.chatbot.service.ChatbotAccessibilityService.Companion  isExtractionRunning @com.coffee.chatbot.service.ChatbotAccessibilityService.Companion  java @com.coffee.chatbot.service.ChatbotAccessibilityService.Companion  launch @com.coffee.chatbot.service.ChatbotAccessibilityService.Companion  listOf @com.coffee.chatbot.service.ChatbotAccessibilityService.Companion  matches @com.coffee.chatbot.service.ChatbotAccessibilityService.Companion  
mutableListOf @com.coffee.chatbot.service.ChatbotAccessibilityService.Companion  rootInActiveWindow @com.coffee.chatbot.service.ChatbotAccessibilityService.Companion  set @com.coffee.chatbot.service.ChatbotAccessibilityService.Companion  sortedBy @com.coffee.chatbot.service.ChatbotAccessibilityService.Companion  
startsWith @com.coffee.chatbot.service.ChatbotAccessibilityService.Companion  until @com.coffee.chatbot.service.ChatbotAccessibilityService.Companion  withContext @com.coffee.chatbot.service.ChatbotAccessibilityService.Companion  bounds Ccom.coffee.chatbot.service.ChatbotAccessibilityService.MessageGroup  text Ccom.coffee.chatbot.service.ChatbotAccessibilityService.MessageGroup  type Ccom.coffee.chatbot.service.ChatbotAccessibilityService.MessageGroup  AccessibilityNodeInfo 1com.coffee.chatbot.service.CustomerServiceHandler  AccessibilityService 1com.coffee.chatbot.service.CustomerServiceHandler  Boolean 1com.coffee.chatbot.service.CustomerServiceHandler  CLICK_MARKER_DURATION 1com.coffee.chatbot.service.CustomerServiceHandler  CONVERSATION_LIST_XPATH 1com.coffee.chatbot.service.CustomerServiceHandler  Color 1com.coffee.chatbot.service.CustomerServiceHandler  Context 1com.coffee.chatbot.service.CustomerServiceHandler  	Exception 1com.coffee.chatbot.service.CustomerServiceHandler  GestureDescription 1com.coffee.chatbot.service.CustomerServiceHandler  Gravity 1com.coffee.chatbot.service.CustomerServiceHandler  Handler 1com.coffee.chatbot.service.CustomerServiceHandler  Int 1com.coffee.chatbot.service.CustomerServiceHandler  List 1com.coffee.chatbot.service.CustomerServiceHandler  Log 1com.coffee.chatbot.service.CustomerServiceHandler  Looper 1com.coffee.chatbot.service.CustomerServiceHandler  MutableList 1com.coffee.chatbot.service.CustomerServiceHandler  Path 1com.coffee.chatbot.service.CustomerServiceHandler  PixelFormat 1com.coffee.chatbot.service.CustomerServiceHandler  Rect 1com.coffee.chatbot.service.CustomerServiceHandler  Regex 1com.coffee.chatbot.service.CustomerServiceHandler  String 1com.coffee.chatbot.service.CustomerServiceHandler  TAG 1com.coffee.chatbot.service.CustomerServiceHandler  TARGET_PACKAGE 1com.coffee.chatbot.service.CustomerServiceHandler  View 1com.coffee.chatbot.service.CustomerServiceHandler  
WindowManager 1com.coffee.chatbot.service.CustomerServiceHandler  accessibilityService 1com.coffee.chatbot.service.CustomerServiceHandler  apply 1com.coffee.chatbot.service.CustomerServiceHandler  checkAndEnterNewMessage 1com.coffee.chatbot.service.CustomerServiceHandler  clickConversation 1com.coffee.chatbot.service.CustomerServiceHandler  contains 1com.coffee.chatbot.service.CustomerServiceHandler  context 1com.coffee.chatbot.service.CustomerServiceHandler  equals 1com.coffee.chatbot.service.CustomerServiceHandler  filter 1com.coffee.chatbot.service.CustomerServiceHandler  findAllTextViews 1com.coffee.chatbot.service.CustomerServiceHandler  findAndClickUnreadConversation 1com.coffee.chatbot.service.CustomerServiceHandler  findChildByClassAndIndex 1com.coffee.chatbot.service.CustomerServiceHandler  findClickableArea 1com.coffee.chatbot.service.CustomerServiceHandler  findNodeByXPath 1com.coffee.chatbot.service.CustomerServiceHandler  findNodesByText 1com.coffee.chatbot.service.CustomerServiceHandler  findNodesByTextRecursive 1com.coffee.chatbot.service.CustomerServiceHandler  findTextViewsRecursive 1com.coffee.chatbot.service.CustomerServiceHandler  findUnreadCount 1com.coffee.chatbot.service.CustomerServiceHandler  findUserNickname 1com.coffee.chatbot.service.CustomerServiceHandler  findWaitingText 1com.coffee.chatbot.service.CustomerServiceHandler  getConversationsFromContainer 1com.coffee.chatbot.service.CustomerServiceHandler  getValue 1com.coffee.chatbot.service.CustomerServiceHandler  handler 1com.coffee.chatbot.service.CustomerServiceHandler  hasUnreadMessage 1com.coffee.chatbot.service.CustomerServiceHandler  isInCustomerServicePage 1com.coffee.chatbot.service.CustomerServiceHandler  
isNotEmpty 1com.coffee.chatbot.service.CustomerServiceHandler  
isNullOrEmpty 1com.coffee.chatbot.service.CustomerServiceHandler  isTargetApp 1com.coffee.chatbot.service.CustomerServiceHandler  isValidConversationItem 1com.coffee.chatbot.service.CustomerServiceHandler  lazy 1com.coffee.chatbot.service.CustomerServiceHandler  let 1com.coffee.chatbot.service.CustomerServiceHandler  
markerView 1com.coffee.chatbot.service.CustomerServiceHandler  matches 1com.coffee.chatbot.service.CustomerServiceHandler  
mutableListOf 1com.coffee.chatbot.service.CustomerServiceHandler  performClick 1com.coffee.chatbot.service.CustomerServiceHandler  printUserNickname 1com.coffee.chatbot.service.CustomerServiceHandler  provideDelegate 1com.coffee.chatbot.service.CustomerServiceHandler  removeClickMarker 1com.coffee.chatbot.service.CustomerServiceHandler  setAccessibilityService 1com.coffee.chatbot.service.CustomerServiceHandler  showClickMarker 1com.coffee.chatbot.service.CustomerServiceHandler  split 1com.coffee.chatbot.service.CustomerServiceHandler  substringAfterLast 1com.coffee.chatbot.service.CustomerServiceHandler  toInt 1com.coffee.chatbot.service.CustomerServiceHandler  toIntOrNull 1com.coffee.chatbot.service.CustomerServiceHandler  trim 1com.coffee.chatbot.service.CustomerServiceHandler  until 1com.coffee.chatbot.service.CustomerServiceHandler  
windowManager 1com.coffee.chatbot.service.CustomerServiceHandler  	withIndex 1com.coffee.chatbot.service.CustomerServiceHandler  GestureResultCallback Fcom.coffee.chatbot.service.CustomerServiceHandler.AccessibilityService  CLICK_MARKER_DURATION ;com.coffee.chatbot.service.CustomerServiceHandler.Companion  CONVERSATION_LIST_XPATH ;com.coffee.chatbot.service.CustomerServiceHandler.Companion  Color ;com.coffee.chatbot.service.CustomerServiceHandler.Companion  Context ;com.coffee.chatbot.service.CustomerServiceHandler.Companion  GestureDescription ;com.coffee.chatbot.service.CustomerServiceHandler.Companion  Gravity ;com.coffee.chatbot.service.CustomerServiceHandler.Companion  Handler ;com.coffee.chatbot.service.CustomerServiceHandler.Companion  Log ;com.coffee.chatbot.service.CustomerServiceHandler.Companion  Looper ;com.coffee.chatbot.service.CustomerServiceHandler.Companion  Path ;com.coffee.chatbot.service.CustomerServiceHandler.Companion  PixelFormat ;com.coffee.chatbot.service.CustomerServiceHandler.Companion  Rect ;com.coffee.chatbot.service.CustomerServiceHandler.Companion  Regex ;com.coffee.chatbot.service.CustomerServiceHandler.Companion  TAG ;com.coffee.chatbot.service.CustomerServiceHandler.Companion  TARGET_PACKAGE ;com.coffee.chatbot.service.CustomerServiceHandler.Companion  View ;com.coffee.chatbot.service.CustomerServiceHandler.Companion  
WindowManager ;com.coffee.chatbot.service.CustomerServiceHandler.Companion  apply ;com.coffee.chatbot.service.CustomerServiceHandler.Companion  contains ;com.coffee.chatbot.service.CustomerServiceHandler.Companion  equals ;com.coffee.chatbot.service.CustomerServiceHandler.Companion  filter ;com.coffee.chatbot.service.CustomerServiceHandler.Companion  getValue ;com.coffee.chatbot.service.CustomerServiceHandler.Companion  
isNotEmpty ;com.coffee.chatbot.service.CustomerServiceHandler.Companion  
isNullOrEmpty ;com.coffee.chatbot.service.CustomerServiceHandler.Companion  lazy ;com.coffee.chatbot.service.CustomerServiceHandler.Companion  let ;com.coffee.chatbot.service.CustomerServiceHandler.Companion  matches ;com.coffee.chatbot.service.CustomerServiceHandler.Companion  
mutableListOf ;com.coffee.chatbot.service.CustomerServiceHandler.Companion  provideDelegate ;com.coffee.chatbot.service.CustomerServiceHandler.Companion  split ;com.coffee.chatbot.service.CustomerServiceHandler.Companion  substringAfterLast ;com.coffee.chatbot.service.CustomerServiceHandler.Companion  toInt ;com.coffee.chatbot.service.CustomerServiceHandler.Companion  toIntOrNull ;com.coffee.chatbot.service.CustomerServiceHandler.Companion  trim ;com.coffee.chatbot.service.CustomerServiceHandler.Companion  until ;com.coffee.chatbot.service.CustomerServiceHandler.Companion  	withIndex ;com.coffee.chatbot.service.CustomerServiceHandler.Companion  Build 0com.coffee.chatbot.service.FloatingWindowService  
CHANNEL_ID 0com.coffee.chatbot.service.FloatingWindowService  ChatbotAccessibilityService 0com.coffee.chatbot.service.FloatingWindowService  	Companion 0com.coffee.chatbot.service.FloatingWindowService  Context 0com.coffee.chatbot.service.FloatingWindowService  CoroutineScope 0com.coffee.chatbot.service.FloatingWindowService  Dispatchers 0com.coffee.chatbot.service.FloatingWindowService  	Exception 0com.coffee.chatbot.service.FloatingWindowService  Gravity 0com.coffee.chatbot.service.FloatingWindowService  Handler 0com.coffee.chatbot.service.FloatingWindowService  IBinder 0com.coffee.chatbot.service.FloatingWindowService  IllegalArgumentException 0com.coffee.chatbot.service.FloatingWindowService  ImageButton 0com.coffee.chatbot.service.FloatingWindowService  Int 0com.coffee.chatbot.service.FloatingWindowService  Intent 0com.coffee.chatbot.service.FloatingWindowService  LayoutInflater 0com.coffee.chatbot.service.FloatingWindowService  Log 0com.coffee.chatbot.service.FloatingWindowService  Looper 0com.coffee.chatbot.service.FloatingWindowService  Math 0com.coffee.chatbot.service.FloatingWindowService  MotionEvent 0com.coffee.chatbot.service.FloatingWindowService  NOTIFICATION_ID 0com.coffee.chatbot.service.FloatingWindowService  Notification 0com.coffee.chatbot.service.FloatingWindowService  NotificationChannel 0com.coffee.chatbot.service.FloatingWindowService  NotificationCompat 0com.coffee.chatbot.service.FloatingWindowService  NotificationManager 0com.coffee.chatbot.service.FloatingWindowService  PixelFormat 0com.coffee.chatbot.service.FloatingWindowService  R 0com.coffee.chatbot.service.FloatingWindowService  START_NOT_STICKY 0com.coffee.chatbot.service.FloatingWindowService  START_STICKY 0com.coffee.chatbot.service.FloatingWindowService  Settings 0com.coffee.chatbot.service.FloatingWindowService  
SupervisorJob 0com.coffee.chatbot.service.FloatingWindowService  TAG 0com.coffee.chatbot.service.FloatingWindowService  Toast 0com.coffee.chatbot.service.FloatingWindowService  View 0com.coffee.chatbot.service.FloatingWindowService  
WindowManager 0com.coffee.chatbot.service.FloatingWindowService  android 0com.coffee.chatbot.service.FloatingWindowService  apply 0com.coffee.chatbot.service.FloatingWindowService  checkBoundaries 0com.coffee.chatbot.service.FloatingWindowService  coroutineScope 0com.coffee.chatbot.service.FloatingWindowService  createFloatingWindow 0com.coffee.chatbot.service.FloatingWindowService  createNotification 0com.coffee.chatbot.service.FloatingWindowService  createNotificationChannel 0com.coffee.chatbot.service.FloatingWindowService  delay 0com.coffee.chatbot.service.FloatingWindowService  floatingView 0com.coffee.chatbot.service.FloatingWindowService  
forceStart 0com.coffee.chatbot.service.FloatingWindowService  getInstance 0com.coffee.chatbot.service.FloatingWindowService  getSystemService 0com.coffee.chatbot.service.FloatingWindowService  handleChatAutomation 0com.coffee.chatbot.service.FloatingWindowService  isAccessibilityServiceEnabled 0com.coffee.chatbot.service.FloatingWindowService  isAutomationRunning 0com.coffee.chatbot.service.FloatingWindowService  
isInitialized 0com.coffee.chatbot.service.FloatingWindowService  isMonitoringMode 0com.coffee.chatbot.service.FloatingWindowService  java 0com.coffee.chatbot.service.FloatingWindowService  launch 0com.coffee.chatbot.service.FloatingWindowService  	resources 0com.coffee.chatbot.service.FloatingWindowService  setupDragMovement 0com.coffee.chatbot.service.FloatingWindowService  
startActivity 0com.coffee.chatbot.service.FloatingWindowService  startForeground 0com.coffee.chatbot.service.FloatingWindowService  stopAutomation 0com.coffee.chatbot.service.FloatingWindowService  stopSelf 0com.coffee.chatbot.service.FloatingWindowService  toggleAutomation 0com.coffee.chatbot.service.FloatingWindowService  updateButtonForMonitoring 0com.coffee.chatbot.service.FloatingWindowService  
windowManager 0com.coffee.chatbot.service.FloatingWindowService  Build :com.coffee.chatbot.service.FloatingWindowService.Companion  
CHANNEL_ID :com.coffee.chatbot.service.FloatingWindowService.Companion  ChatbotAccessibilityService :com.coffee.chatbot.service.FloatingWindowService.Companion  Context :com.coffee.chatbot.service.FloatingWindowService.Companion  CoroutineScope :com.coffee.chatbot.service.FloatingWindowService.Companion  Dispatchers :com.coffee.chatbot.service.FloatingWindowService.Companion  Gravity :com.coffee.chatbot.service.FloatingWindowService.Companion  Handler :com.coffee.chatbot.service.FloatingWindowService.Companion  Intent :com.coffee.chatbot.service.FloatingWindowService.Companion  LayoutInflater :com.coffee.chatbot.service.FloatingWindowService.Companion  Log :com.coffee.chatbot.service.FloatingWindowService.Companion  Looper :com.coffee.chatbot.service.FloatingWindowService.Companion  Math :com.coffee.chatbot.service.FloatingWindowService.Companion  MotionEvent :com.coffee.chatbot.service.FloatingWindowService.Companion  NOTIFICATION_ID :com.coffee.chatbot.service.FloatingWindowService.Companion  NotificationChannel :com.coffee.chatbot.service.FloatingWindowService.Companion  NotificationCompat :com.coffee.chatbot.service.FloatingWindowService.Companion  NotificationManager :com.coffee.chatbot.service.FloatingWindowService.Companion  PixelFormat :com.coffee.chatbot.service.FloatingWindowService.Companion  R :com.coffee.chatbot.service.FloatingWindowService.Companion  START_NOT_STICKY :com.coffee.chatbot.service.FloatingWindowService.Companion  START_STICKY :com.coffee.chatbot.service.FloatingWindowService.Companion  Settings :com.coffee.chatbot.service.FloatingWindowService.Companion  
SupervisorJob :com.coffee.chatbot.service.FloatingWindowService.Companion  TAG :com.coffee.chatbot.service.FloatingWindowService.Companion  Toast :com.coffee.chatbot.service.FloatingWindowService.Companion  
WindowManager :com.coffee.chatbot.service.FloatingWindowService.Companion  android :com.coffee.chatbot.service.FloatingWindowService.Companion  apply :com.coffee.chatbot.service.FloatingWindowService.Companion  delay :com.coffee.chatbot.service.FloatingWindowService.Companion  floatingView :com.coffee.chatbot.service.FloatingWindowService.Companion  
forceStart :com.coffee.chatbot.service.FloatingWindowService.Companion  getInstance :com.coffee.chatbot.service.FloatingWindowService.Companion  isAccessibilityServiceEnabled :com.coffee.chatbot.service.FloatingWindowService.Companion  isAutomationRunning :com.coffee.chatbot.service.FloatingWindowService.Companion  
isInitialized :com.coffee.chatbot.service.FloatingWindowService.Companion  isMonitoringMode :com.coffee.chatbot.service.FloatingWindowService.Companion  java :com.coffee.chatbot.service.FloatingWindowService.Companion  launch :com.coffee.chatbot.service.FloatingWindowService.Companion  	resources :com.coffee.chatbot.service.FloatingWindowService.Companion  
startActivity :com.coffee.chatbot.service.FloatingWindowService.Companion  LayoutParams >com.coffee.chatbot.service.FloatingWindowService.WindowManager  axis #com.coffee.chatbot.service.PathStep  segment #com.coffee.chatbot.service.PathStep  AccessibilityNodeInfo )com.coffee.chatbot.service.WebViewService  AtomicReference )com.coffee.chatbot.service.WebViewService  Build )com.coffee.chatbot.service.WebViewService  
CHANNEL_ID )com.coffee.chatbot.service.WebViewService  	Companion )com.coffee.chatbot.service.WebViewService  DEFAULT_PORT )com.coffee.chatbot.service.WebViewService  	Exception )com.coffee.chatbot.service.WebViewService  IBinder )com.coffee.chatbot.service.WebViewService  IHTTPSession )com.coffee.chatbot.service.WebViewService  IOException )com.coffee.chatbot.service.WebViewService  Int )com.coffee.chatbot.service.WebViewService  Intent )com.coffee.chatbot.service.WebViewService  Log )com.coffee.chatbot.service.WebViewService  NOTIFICATION_ID )com.coffee.chatbot.service.WebViewService  	NanoHTTPD )com.coffee.chatbot.service.WebViewService  NetworkInterface )com.coffee.chatbot.service.WebViewService  NodeHierarchyServer )com.coffee.chatbot.service.WebViewService  Notification )com.coffee.chatbot.service.WebViewService  NotificationChannel )com.coffee.chatbot.service.WebViewService  NotificationCompat )com.coffee.chatbot.service.WebViewService  NotificationManager )com.coffee.chatbot.service.WebViewService  R )com.coffee.chatbot.service.WebViewService  Response )com.coffee.chatbot.service.WebViewService  String )com.coffee.chatbot.service.WebViewService  
StringBuilder )com.coffee.chatbot.service.WebViewService  TAG )com.coffee.chatbot.service.WebViewService  WebViewService )com.coffee.chatbot.service.WebViewService  
appendNodeXml )com.coffee.chatbot.service.WebViewService  apply )com.coffee.chatbot.service.WebViewService  
asSequence )com.coffee.chatbot.service.WebViewService  buildNodeDescription )com.coffee.chatbot.service.WebViewService  createNotification )com.coffee.chatbot.service.WebViewService  createNotificationChannel )com.coffee.chatbot.service.WebViewService  currentRootNode )com.coffee.chatbot.service.WebViewService  
escapeHtml )com.coffee.chatbot.service.WebViewService  forEach )com.coffee.chatbot.service.WebViewService  generateHtml )com.coffee.chatbot.service.WebViewService  generateNodeXml )com.coffee.chatbot.service.WebViewService  
getChildIndex )com.coffee.chatbot.service.WebViewService  getInstance )com.coffee.chatbot.service.WebViewService  getLocalIpAddress )com.coffee.chatbot.service.WebViewService  getSystemService )com.coffee.chatbot.service.WebViewService  indexOf )com.coffee.chatbot.service.WebViewService  instance )com.coffee.chatbot.service.WebViewService  java )com.coffee.chatbot.service.WebViewService  let )com.coffee.chatbot.service.WebViewService  newFixedLengthResponse )com.coffee.chatbot.service.WebViewService  renderNodeAsHtml )com.coffee.chatbot.service.WebViewService  repeat )com.coffee.chatbot.service.WebViewService  replace )com.coffee.chatbot.service.WebViewService  server )com.coffee.chatbot.service.WebViewService  startForeground )com.coffee.chatbot.service.WebViewService  substringAfterLast )com.coffee.chatbot.service.WebViewService  until )com.coffee.chatbot.service.WebViewService  updateRootNode )com.coffee.chatbot.service.WebViewService  AccessibilityNodeInfo 3com.coffee.chatbot.service.WebViewService.Companion  AtomicReference 3com.coffee.chatbot.service.WebViewService.Companion  Build 3com.coffee.chatbot.service.WebViewService.Companion  
CHANNEL_ID 3com.coffee.chatbot.service.WebViewService.Companion  DEFAULT_PORT 3com.coffee.chatbot.service.WebViewService.Companion  Log 3com.coffee.chatbot.service.WebViewService.Companion  NOTIFICATION_ID 3com.coffee.chatbot.service.WebViewService.Companion  NetworkInterface 3com.coffee.chatbot.service.WebViewService.Companion  NotificationChannel 3com.coffee.chatbot.service.WebViewService.Companion  NotificationCompat 3com.coffee.chatbot.service.WebViewService.Companion  NotificationManager 3com.coffee.chatbot.service.WebViewService.Companion  R 3com.coffee.chatbot.service.WebViewService.Companion  Response 3com.coffee.chatbot.service.WebViewService.Companion  
StringBuilder 3com.coffee.chatbot.service.WebViewService.Companion  TAG 3com.coffee.chatbot.service.WebViewService.Companion  apply 3com.coffee.chatbot.service.WebViewService.Companion  
asSequence 3com.coffee.chatbot.service.WebViewService.Companion  forEach 3com.coffee.chatbot.service.WebViewService.Companion  generateHtml 3com.coffee.chatbot.service.WebViewService.Companion  getInstance 3com.coffee.chatbot.service.WebViewService.Companion  getLocalIpAddress 3com.coffee.chatbot.service.WebViewService.Companion  indexOf 3com.coffee.chatbot.service.WebViewService.Companion  instance 3com.coffee.chatbot.service.WebViewService.Companion  java 3com.coffee.chatbot.service.WebViewService.Companion  let 3com.coffee.chatbot.service.WebViewService.Companion  newFixedLengthResponse 3com.coffee.chatbot.service.WebViewService.Companion  repeat 3com.coffee.chatbot.service.WebViewService.Companion  replace 3com.coffee.chatbot.service.WebViewService.Companion  substringAfterLast 3com.coffee.chatbot.service.WebViewService.Companion  until 3com.coffee.chatbot.service.WebViewService.Companion  Response =com.coffee.chatbot.service.WebViewService.NodeHierarchyServer  generateHtml =com.coffee.chatbot.service.WebViewService.NodeHierarchyServer  newFixedLengthResponse =com.coffee.chatbot.service.WebViewService.NodeHierarchyServer  start =com.coffee.chatbot.service.WebViewService.NodeHierarchyServer  stop =com.coffee.chatbot.service.WebViewService.NodeHierarchyServer  LayoutParams (com.coffee.chatbot.service.WindowManager  Boolean com.coffee.chatbot.ui.theme  Build com.coffee.chatbot.ui.theme  ChatbotTheme com.coffee.chatbot.ui.theme  
Composable com.coffee.chatbot.ui.theme  DarkColorScheme com.coffee.chatbot.ui.theme  
FontFamily com.coffee.chatbot.ui.theme  
FontWeight com.coffee.chatbot.ui.theme  LightColorScheme com.coffee.chatbot.ui.theme  Pink40 com.coffee.chatbot.ui.theme  Pink80 com.coffee.chatbot.ui.theme  Purple40 com.coffee.chatbot.ui.theme  Purple80 com.coffee.chatbot.ui.theme  PurpleGrey40 com.coffee.chatbot.ui.theme  PurpleGrey80 com.coffee.chatbot.ui.theme  
Typography com.coffee.chatbot.ui.theme  Unit com.coffee.chatbot.ui.theme  	NanoHTTPD 
fi.iki.elonen  DEFAULT_PORT fi.iki.elonen.NanoHTTPD  IHTTPSession fi.iki.elonen.NanoHTTPD  Response fi.iki.elonen.NanoHTTPD  generateHtml fi.iki.elonen.NanoHTTPD  newFixedLengthResponse fi.iki.elonen.NanoHTTPD  start fi.iki.elonen.NanoHTTPD  stop fi.iki.elonen.NanoHTTPD  Status  fi.iki.elonen.NanoHTTPD.Response  OK 'fi.iki.elonen.NanoHTTPD.Response.Status  IOException java.io  Class 	java.lang  	Exception 	java.lang  IllegalArgumentException 	java.lang  Runnable 	java.lang  
StringBuilder 	java.lang  
canonicalName java.lang.Class  abs java.lang.Math  <SAM-CONSTRUCTOR> java.lang.Runnable  append java.lang.StringBuilder  toString java.lang.StringBuilder  currentTimeMillis java.lang.System  NetworkInterface java.net  hostAddress java.net.InetAddress  isLoopbackAddress java.net.InetAddress  getNetworkInterfaces java.net.NetworkInterface  
inetAddresses java.net.NetworkInterface  
ArrayDeque 	java.util  addLast java.util.ArrayDeque  
isNotEmpty java.util.ArrayDeque  removeFirst java.util.ArrayDeque  ConcurrentHashMap java.util.concurrent  clear &java.util.concurrent.ConcurrentHashMap  containsKey &java.util.concurrent.ConcurrentHashMap  set &java.util.concurrent.ConcurrentHashMap  values &java.util.concurrent.ConcurrentHashMap  AtomicReference java.util.concurrent.atomic  get +java.util.concurrent.atomic.AtomicReference  	getAndSet +java.util.concurrent.atomic.AtomicReference  Pattern java.util.regex  find java.util.regex.Matcher  group java.util.regex.Matcher  matches java.util.regex.Matcher  compile java.util.regex.Pattern  matcher java.util.regex.Pattern  CharSequence kotlin  	Function0 kotlin  	Function1 kotlin  	Function2 kotlin  Lazy kotlin  Nothing kotlin  Pair kotlin  apply kotlin  getValue kotlin  
isInitialized kotlin  lazy kotlin  let kotlin  repeat kotlin  toList kotlin  equals 
kotlin.Any  toString 
kotlin.Any  not kotlin.Boolean  toString kotlin.Boolean  isEmpty kotlin.CharSequence  let kotlin.CharSequence  toString kotlin.CharSequence  sp 
kotlin.Double  	compareTo kotlin.Float  minus kotlin.Float  toInt kotlin.Float  invoke kotlin.Function1  	compareTo 
kotlin.Int  div 
kotlin.Int  inc 
kotlin.Int  minus 
kotlin.Int  or 
kotlin.Int  plus 
kotlin.Int  rangeTo 
kotlin.Int  times 
kotlin.Int  toFloat 
kotlin.Int  getValue kotlin.Lazy  provideDelegate kotlin.Lazy  
component1 kotlin.Pair  
component2 kotlin.Pair  contains 
kotlin.String  endsWith 
kotlin.String  equals 
kotlin.String  isBlank 
kotlin.String  isEmpty 
kotlin.String  
isNotEmpty 
kotlin.String  
isNullOrEmpty 
kotlin.String  length 
kotlin.String  let 
kotlin.String  	lowercase 
kotlin.String  matches 
kotlin.String  repeat 
kotlin.String  replace 
kotlin.String  split 
kotlin.String  
startsWith 
kotlin.String  	substring 
kotlin.String  substringAfter 
kotlin.String  substringAfterLast 
kotlin.String  toInt 
kotlin.String  toIntOrNull 
kotlin.String  trim 
kotlin.String  IndexedValue kotlin.collections  IntIterator kotlin.collections  Iterable kotlin.collections  Iterator kotlin.collections  List kotlin.collections  Map kotlin.collections  MutableCollection kotlin.collections  MutableIterator kotlin.collections  MutableList kotlin.collections  
MutableSet kotlin.collections  Set kotlin.collections  all kotlin.collections  any kotlin.collections  
asSequence kotlin.collections  contains kotlin.collections  	emptyList kotlin.collections  filter kotlin.collections  
filterIndexed kotlin.collections  	filterNot kotlin.collections  firstOrNull kotlin.collections  forEach kotlin.collections  getValue kotlin.collections  indexOf kotlin.collections  isEmpty kotlin.collections  
isNotEmpty kotlin.collections  
isNullOrEmpty kotlin.collections  listOf kotlin.collections  
listOfNotNull kotlin.collections  
mapNotNull kotlin.collections  
mutableListOf kotlin.collections  mutableSetOf kotlin.collections  set kotlin.collections  setOf kotlin.collections  sortedBy kotlin.collections  toList kotlin.collections  	withIndex kotlin.collections  
component1 kotlin.collections.IndexedValue  
component2 kotlin.collections.IndexedValue  hasNext kotlin.collections.IntIterator  next kotlin.collections.IntIterator  iterator kotlin.collections.Iterable  hasNext kotlin.collections.Iterator  next kotlin.collections.Iterator  all kotlin.collections.List  any kotlin.collections.List  contains kotlin.collections.List  filter kotlin.collections.List  
filterIndexed kotlin.collections.List  	filterNot kotlin.collections.List  firstOrNull kotlin.collections.List  get kotlin.collections.List  isEmpty kotlin.collections.List  
isNotEmpty kotlin.collections.List  iterator kotlin.collections.List  size kotlin.collections.List  	withIndex kotlin.collections.List  Entry kotlin.collections.Map  sortedBy $kotlin.collections.MutableCollection  hasNext "kotlin.collections.MutableIterator  next "kotlin.collections.MutableIterator  add kotlin.collections.MutableList  iterator kotlin.collections.MutableList  add kotlin.collections.MutableSet  addAll kotlin.collections.MutableSet  iterator kotlin.collections.Set  toList kotlin.collections.Set  CoroutineContext kotlin.coroutines  SuspendFunction1 kotlin.coroutines  plus "kotlin.coroutines.CoroutineContext  endsWith 	kotlin.io  
startsWith 	kotlin.io  Volatile 
kotlin.jvm  java 
kotlin.jvm  	CharRange 
kotlin.ranges  IntRange 
kotlin.ranges  	LongRange 
kotlin.ranges  	UIntRange 
kotlin.ranges  
ULongRange 
kotlin.ranges  contains 
kotlin.ranges  firstOrNull 
kotlin.ranges  until 
kotlin.ranges  iterator kotlin.ranges.IntProgression  contains kotlin.ranges.IntRange  iterator kotlin.ranges.IntRange  
mapNotNull kotlin.ranges.IntRange  KMutableProperty0 kotlin.reflect  KMutableProperty1 kotlin.reflect  
KProperty1 kotlin.reflect  java kotlin.reflect.KClass  
isInitialized  kotlin.reflect.KMutableProperty0  Sequence kotlin.sequences  all kotlin.sequences  any kotlin.sequences  
asSequence kotlin.sequences  contains kotlin.sequences  filter kotlin.sequences  
filterIndexed kotlin.sequences  	filterNot kotlin.sequences  firstOrNull kotlin.sequences  forEach kotlin.sequences  indexOf kotlin.sequences  
mapNotNull kotlin.sequences  sortedBy kotlin.sequences  toList kotlin.sequences  	withIndex kotlin.sequences  forEach kotlin.sequences.Sequence  MatchResult kotlin.text  Regex kotlin.text  all kotlin.text  any kotlin.text  
asSequence kotlin.text  contains kotlin.text  endsWith kotlin.text  equals kotlin.text  filter kotlin.text  
filterIndexed kotlin.text  	filterNot kotlin.text  firstOrNull kotlin.text  forEach kotlin.text  indexOf kotlin.text  isBlank kotlin.text  isEmpty kotlin.text  
isNotEmpty kotlin.text  
isNullOrEmpty kotlin.text  	lowercase kotlin.text  
mapNotNull kotlin.text  matches kotlin.text  repeat kotlin.text  replace kotlin.text  set kotlin.text  split kotlin.text  
startsWith kotlin.text  	substring kotlin.text  substringAfter kotlin.text  substringAfterLast kotlin.text  toInt kotlin.text  toIntOrNull kotlin.text  toList kotlin.text  toRegex kotlin.text  trim kotlin.text  	withIndex kotlin.text  groupValues kotlin.text.MatchResult  find kotlin.text.Regex  AccessibilityEvent kotlinx.coroutines  AccessibilityNodeInfo kotlinx.coroutines  AccessibilityService kotlinx.coroutines  Boolean kotlinx.coroutines  Bundle kotlinx.coroutines  ChatMessage kotlinx.coroutines  ChatbotAccessibilityService kotlinx.coroutines  CompletableJob kotlinx.coroutines  ConcurrentHashMap kotlinx.coroutines  Context kotlinx.coroutines  CoroutineDispatcher kotlinx.coroutines  CoroutineScope kotlinx.coroutines  CustomerServiceHandler kotlinx.coroutines  Delay kotlinx.coroutines  Dispatchers kotlinx.coroutines  	Exception kotlinx.coroutines  GLOBAL_ACTION_BACK kotlinx.coroutines  Handler kotlinx.coroutines  Int kotlinx.coroutines  Intent kotlinx.coroutines  Job kotlinx.coroutines  List kotlinx.coroutines  Log kotlinx.coroutines  Looper kotlinx.coroutines  MainCoroutineDispatcher kotlinx.coroutines  MessageGroup kotlinx.coroutines  MutableList kotlinx.coroutines  Rect kotlinx.coroutines  Regex kotlinx.coroutines  Runnable kotlinx.coroutines  Settings kotlinx.coroutines  String kotlinx.coroutines  
SupervisorJob kotlinx.coroutines  TAG kotlinx.coroutines  	TextUtils kotlinx.coroutines  Unit kotlinx.coroutines  Volatile kotlinx.coroutines  WebViewService kotlinx.coroutines  any kotlinx.coroutines  apply kotlinx.coroutines  cancel kotlinx.coroutines  chatHistoryCallback kotlinx.coroutines  contains kotlinx.coroutines  delay kotlinx.coroutines  equals kotlinx.coroutines  extractChatHistory kotlinx.coroutines  findNodeByClassName kotlinx.coroutines  findNodeByText kotlinx.coroutines  findScrollableNode kotlinx.coroutines  getInstance kotlinx.coroutines  handleCustomerServicePage kotlinx.coroutines  instance kotlinx.coroutines  isBlank kotlinx.coroutines  isExtractionRunning kotlinx.coroutines  java kotlinx.coroutines  launch kotlinx.coroutines  listOf kotlinx.coroutines  matches kotlinx.coroutines  
mutableListOf kotlinx.coroutines  rootInActiveWindow kotlinx.coroutines  set kotlinx.coroutines  sortedBy kotlinx.coroutines  
startsWith kotlinx.coroutines  until kotlinx.coroutines  withContext kotlinx.coroutines  plus &kotlinx.coroutines.CoroutineDispatcher  AccessibilityNodeInfo !kotlinx.coroutines.CoroutineScope  Bundle !kotlinx.coroutines.CoroutineScope  ChatbotAccessibilityService !kotlinx.coroutines.CoroutineScope  Dispatchers !kotlinx.coroutines.CoroutineScope  Intent !kotlinx.coroutines.CoroutineScope  Log !kotlinx.coroutines.CoroutineScope  R !kotlinx.coroutines.CoroutineScope  Settings !kotlinx.coroutines.CoroutineScope  TAG !kotlinx.coroutines.CoroutineScope  Toast !kotlinx.coroutines.CoroutineScope  WebViewService !kotlinx.coroutines.CoroutineScope  android !kotlinx.coroutines.CoroutineScope  cancel !kotlinx.coroutines.CoroutineScope  delay !kotlinx.coroutines.CoroutineScope  extractChatHistory !kotlinx.coroutines.CoroutineScope  findNodeByClassName !kotlinx.coroutines.CoroutineScope  findNodeByText !kotlinx.coroutines.CoroutineScope  findScrollableNode !kotlinx.coroutines.CoroutineScope  floatingView !kotlinx.coroutines.CoroutineScope  
forceStart !kotlinx.coroutines.CoroutineScope  getInstance !kotlinx.coroutines.CoroutineScope  handleCustomerServicePage !kotlinx.coroutines.CoroutineScope  isAccessibilityServiceEnabled !kotlinx.coroutines.CoroutineScope  isAutomationRunning !kotlinx.coroutines.CoroutineScope  isExtractionRunning !kotlinx.coroutines.CoroutineScope  isMonitoringMode !kotlinx.coroutines.CoroutineScope  launch !kotlinx.coroutines.CoroutineScope  rootInActiveWindow !kotlinx.coroutines.CoroutineScope  
startActivity !kotlinx.coroutines.CoroutineScope  withContext !kotlinx.coroutines.CoroutineScope  Default kotlinx.coroutines.Dispatchers  Main kotlinx.coroutines.Dispatchers  plus *kotlinx.coroutines.MainCoroutineDispatcher  XPathResult android.graphics  android android.graphics  describe android.graphics  findAll android.graphics  
findAllByText android.graphics  
findClickable android.graphics  findSendButton android.graphics  findUnreadConversations android.graphics  forEachIndexed android.graphics  
isNotBlank android.graphics  	printTree android.graphics  xpath android.graphics  xpathUse android.graphics  AccessibilityNodeInfo android.os.Bundle  apply android.os.Bundle  XPathResult 0android.view.accessibility.AccessibilityNodeInfo  convertCssSelectorToXPath 0android.view.accessibility.AccessibilityNodeInfo  count 0android.view.accessibility.AccessibilityNodeInfo  describe 0android.view.accessibility.AccessibilityNodeInfo  filter 0android.view.accessibility.AccessibilityNodeInfo  find 0android.view.accessibility.AccessibilityNodeInfo  findAll 0android.view.accessibility.AccessibilityNodeInfo  
findAllByText 0android.view.accessibility.AccessibilityNodeInfo  findById 0android.view.accessibility.AccessibilityNodeInfo  
findByText 0android.view.accessibility.AccessibilityNodeInfo  
findClickable 0android.view.accessibility.AccessibilityNodeInfo  findSendButton 0android.view.accessibility.AccessibilityNodeInfo  findUnreadConversations 0android.view.accessibility.AccessibilityNodeInfo  getXPath 0android.view.accessibility.AccessibilityNodeInfo  invoke 0android.view.accessibility.AccessibilityNodeInfo  joinToString 0android.view.accessibility.AccessibilityNodeInfo  
mutableListOf 0android.view.accessibility.AccessibilityNodeInfo  	printTree 0android.view.accessibility.AccessibilityNodeInfo  println 0android.view.accessibility.AccessibilityNodeInfo  repeat 0android.view.accessibility.AccessibilityNodeInfo  select 0android.view.accessibility.AccessibilityNodeInfo  substringAfterLast 0android.view.accessibility.AccessibilityNodeInfo  use 0android.view.accessibility.AccessibilityNodeInfo  xpath 0android.view.accessibility.AccessibilityNodeInfo  xpathUse 0android.view.accessibility.AccessibilityNodeInfo  T com.coffee.chatbot.service  XPathResult com.coffee.chatbot.service  XPathUsageExamples com.coffee.chatbot.service  convertCssSelectorToXPath com.coffee.chatbot.service  count com.coffee.chatbot.service  describe com.coffee.chatbot.service  find com.coffee.chatbot.service  findAll com.coffee.chatbot.service  
findAllByText com.coffee.chatbot.service  findById com.coffee.chatbot.service  
findByText com.coffee.chatbot.service  
findClickable com.coffee.chatbot.service   findCustomerServiceConversations com.coffee.chatbot.service  findMessageInput com.coffee.chatbot.service  findSendButton com.coffee.chatbot.service  findUnreadConversations com.coffee.chatbot.service  findUnreadMessages com.coffee.chatbot.service  forEachIndexed com.coffee.chatbot.service  getNodeAttribute com.coffee.chatbot.service  	getOrNull com.coffee.chatbot.service  getXPath com.coffee.chatbot.service  
isNotBlank com.coffee.chatbot.service  joinToString com.coffee.chatbot.service  
lastOrNull com.coffee.chatbot.service  	printTree com.coffee.chatbot.service  println com.coffee.chatbot.service  select com.coffee.chatbot.service  use com.coffee.chatbot.service  xpath com.coffee.chatbot.service  xpathUse com.coffee.chatbot.service  XPathResult 1com.coffee.chatbot.service.CustomerServiceHandler  android 1com.coffee.chatbot.service.CustomerServiceHandler  clickFirstUnreadConversation 1com.coffee.chatbot.service.CustomerServiceHandler  describe 1com.coffee.chatbot.service.CustomerServiceHandler  findAll 1com.coffee.chatbot.service.CustomerServiceHandler  
findAllByText 1com.coffee.chatbot.service.CustomerServiceHandler  'findAndClickUnreadConversationOptimized 1com.coffee.chatbot.service.CustomerServiceHandler  
findClickable 1com.coffee.chatbot.service.CustomerServiceHandler  findSendButton 1com.coffee.chatbot.service.CustomerServiceHandler  findUnreadConversations 1com.coffee.chatbot.service.CustomerServiceHandler  forEachIndexed 1com.coffee.chatbot.service.CustomerServiceHandler  
isNotBlank 1com.coffee.chatbot.service.CustomerServiceHandler  performClickOnNode 1com.coffee.chatbot.service.CustomerServiceHandler  performGestureClick 1com.coffee.chatbot.service.CustomerServiceHandler  printConversationInfo 1com.coffee.chatbot.service.CustomerServiceHandler  	printTree 1com.coffee.chatbot.service.CustomerServiceHandler  xpath 1com.coffee.chatbot.service.CustomerServiceHandler  xpathUse 1com.coffee.chatbot.service.CustomerServiceHandler  AccessibilityNodeInfo ;com.coffee.chatbot.service.CustomerServiceHandler.Companion  android ;com.coffee.chatbot.service.CustomerServiceHandler.Companion  describe ;com.coffee.chatbot.service.CustomerServiceHandler.Companion  findAll ;com.coffee.chatbot.service.CustomerServiceHandler.Companion  
findAllByText ;com.coffee.chatbot.service.CustomerServiceHandler.Companion  
findClickable ;com.coffee.chatbot.service.CustomerServiceHandler.Companion  findSendButton ;com.coffee.chatbot.service.CustomerServiceHandler.Companion  findUnreadConversations ;com.coffee.chatbot.service.CustomerServiceHandler.Companion  forEachIndexed ;com.coffee.chatbot.service.CustomerServiceHandler.Companion  
isNotBlank ;com.coffee.chatbot.service.CustomerServiceHandler.Companion  	printTree ;com.coffee.chatbot.service.CustomerServiceHandler.Companion  xpath ;com.coffee.chatbot.service.CustomerServiceHandler.Companion  xpathUse ;com.coffee.chatbot.service.CustomerServiceHandler.Companion  AccessibilityNodeInfo &com.coffee.chatbot.service.XPathResult  XPathResult &com.coffee.chatbot.service.XPathResult  click &com.coffee.chatbot.service.XPathResult  exists &com.coffee.chatbot.service.XPathResult  findNodesByXPath &com.coffee.chatbot.service.XPathResult  first &com.coffee.chatbot.service.XPathResult  firstOrNull &com.coffee.chatbot.service.XPathResult  	getOrNull &com.coffee.chatbot.service.XPathResult  
isNotEmpty &com.coffee.chatbot.service.XPathResult  
lastOrNull &com.coffee.chatbot.service.XPathResult  
mapNotNull &com.coffee.chatbot.service.XPathResult  
mutableListOf &com.coffee.chatbot.service.XPathResult  nodes &com.coffee.chatbot.service.XPathResult  parent &com.coffee.chatbot.service.XPathResult  recycle &com.coffee.chatbot.service.XPathResult  size &com.coffee.chatbot.service.XPathResult  text &com.coffee.chatbot.service.XPathResult  texts &com.coffee.chatbot.service.XPathResult  xpath &com.coffee.chatbot.service.XPathResult  AccessibilityNodeInfo -com.coffee.chatbot.service.XPathUsageExamples  describe -com.coffee.chatbot.service.XPathUsageExamples  find -com.coffee.chatbot.service.XPathUsageExamples  findAll -com.coffee.chatbot.service.XPathUsageExamples  findById -com.coffee.chatbot.service.XPathUsageExamples  
findByText -com.coffee.chatbot.service.XPathUsageExamples  
findClickable -com.coffee.chatbot.service.XPathUsageExamples  getXPath -com.coffee.chatbot.service.XPathUsageExamples  let -com.coffee.chatbot.service.XPathUsageExamples  	printTree -com.coffee.chatbot.service.XPathUsageExamples  println -com.coffee.chatbot.service.XPathUsageExamples  select -com.coffee.chatbot.service.XPathUsageExamples  use -com.coffee.chatbot.service.XPathUsageExamples  xpath -com.coffee.chatbot.service.XPathUsageExamples  xpathUse -com.coffee.chatbot.service.XPathUsageExamples  message java.lang.Exception  let kotlin.Boolean  invoke 
kotlin.String  
isNotBlank 
kotlin.String  plus 
kotlin.String  message kotlin.Throwable  count kotlin.collections  forEachIndexed kotlin.collections  	getOrNull kotlin.collections  joinToString kotlin.collections  
lastOrNull kotlin.collections  forEachIndexed kotlin.collections.List  	getOrNull kotlin.collections.List  joinToString kotlin.collections.List  
lastOrNull kotlin.collections.List  
mapNotNull kotlin.collections.List  addAll kotlin.collections.MutableList  
isNotEmpty kotlin.collections.MutableList  joinToString kotlin.collections.MutableList  println 	kotlin.io  
lastOrNull 
kotlin.ranges  count kotlin.ranges.IntRange  count kotlin.sequences  forEachIndexed kotlin.sequences  joinToString kotlin.sequences  
lastOrNull kotlin.sequences  count kotlin.text  forEachIndexed kotlin.text  	getOrNull kotlin.text  
isNotBlank kotlin.text  
lastOrNull kotlin.text  any android.graphics  firstOrNull android.graphics  any 1com.coffee.chatbot.service.CustomerServiceHandler  firstOrNull 1com.coffee.chatbot.service.CustomerServiceHandler  any ;com.coffee.chatbot.service.CustomerServiceHandler.Companion  firstOrNull ;com.coffee.chatbot.service.CustomerServiceHandler.Companion  any &com.coffee.chatbot.service.XPathResult  joinToString android.graphics  joinToString 1com.coffee.chatbot.service.CustomerServiceHandler  joinToString ;com.coffee.chatbot.service.CustomerServiceHandler.Companion  getXPath android.graphics  getXPath 1com.coffee.chatbot.service.CustomerServiceHandler  getXPath ;com.coffee.chatbot.service.CustomerServiceHandler.Companion  UNREAD_INDICATOR_XPATH android.graphics  listOf android.graphics  UNREAD_INDICATOR_XPATH com.coffee.chatbot.service  UNREAD_INDICATOR_XPATH 1com.coffee.chatbot.service.CustomerServiceHandler  listOf 1com.coffee.chatbot.service.CustomerServiceHandler  UNREAD_INDICATOR_XPATH ;com.coffee.chatbot.service.CustomerServiceHandler.Companion  XPathResult ;com.coffee.chatbot.service.CustomerServiceHandler.Companion  listOf ;com.coffee.chatbot.service.CustomerServiceHandler.Companion  LAST_MESSAGE_XPATH android.graphics  NICKNAME_XPATH android.graphics  
TIME_XPATH android.graphics  LAST_MESSAGE_XPATH com.coffee.chatbot.service  NICKNAME_XPATH com.coffee.chatbot.service  
TIME_XPATH com.coffee.chatbot.service  LAST_MESSAGE_XPATH 1com.coffee.chatbot.service.CustomerServiceHandler  NICKNAME_XPATH 1com.coffee.chatbot.service.CustomerServiceHandler  
TIME_XPATH 1com.coffee.chatbot.service.CustomerServiceHandler  LAST_MESSAGE_XPATH ;com.coffee.chatbot.service.CustomerServiceHandler.Companion  NICKNAME_XPATH ;com.coffee.chatbot.service.CustomerServiceHandler.Companion  
TIME_XPATH ;com.coffee.chatbot.service.CustomerServiceHandler.Companion  Math android.graphics  Pair android.graphics  System android.graphics  Thread android.graphics  System com.coffee.chatbot.service  Thread com.coffee.chatbot.service  Math 1com.coffee.chatbot.service.CustomerServiceHandler  Pair 1com.coffee.chatbot.service.CustomerServiceHandler  System 1com.coffee.chatbot.service.CustomerServiceHandler  Thread 1com.coffee.chatbot.service.CustomerServiceHandler  checkForChatFeatures 1com.coffee.chatbot.service.CustomerServiceHandler  findCenterClickableArea 1com.coffee.chatbot.service.CustomerServiceHandler  findClickableParent 1com.coffee.chatbot.service.CustomerServiceHandler   performMultiPositionGestureClick 1com.coffee.chatbot.service.CustomerServiceHandler  performSmartClick 1com.coffee.chatbot.service.CustomerServiceHandler  showClickMarkerIfEnabled 1com.coffee.chatbot.service.CustomerServiceHandler  verifyClickSuccess 1com.coffee.chatbot.service.CustomerServiceHandler  Math ;com.coffee.chatbot.service.CustomerServiceHandler.Companion  Pair ;com.coffee.chatbot.service.CustomerServiceHandler.Companion  System ;com.coffee.chatbot.service.CustomerServiceHandler.Companion  Thread ;com.coffee.chatbot.service.CustomerServiceHandler.Companion  iterator &com.coffee.chatbot.service.XPathResult  sleep java.lang.Thread  	compareTo kotlin.Long  minus kotlin.Long  
isNotEmpty android.app.Service  joinToString android.app.Service  
isNotEmpty android.content.Context  joinToString android.content.Context  
isNotEmpty android.content.ContextWrapper  joinToString android.content.ContextWrapper  bottom android.graphics.Rect  isCheckable 0android.view.accessibility.AccessibilityNodeInfo  android )com.coffee.chatbot.service.WebViewService  
isNotEmpty )com.coffee.chatbot.service.WebViewService  joinToString )com.coffee.chatbot.service.WebViewService  
mutableListOf )com.coffee.chatbot.service.WebViewService  android 3com.coffee.chatbot.service.WebViewService.Companion  
isNotEmpty 3com.coffee.chatbot.service.WebViewService.Companion  joinToString 3com.coffee.chatbot.service.WebViewService.Companion  
mutableListOf 3com.coffee.chatbot.service.WebViewService.Companion  ChatDetailResult android.graphics  ChatDetailResult com.coffee.chatbot.service  chatHistorySummary +com.coffee.chatbot.service.ChatDetailResult  isInChatDetailPage +com.coffee.chatbot.service.ChatDetailResult  ChatDetailResult 1com.coffee.chatbot.service.CustomerServiceHandler  getChatHistorySummary 1com.coffee.chatbot.service.CustomerServiceHandler  isInChatDetailPage 1com.coffee.chatbot.service.CustomerServiceHandler  ChatDetailResult ;com.coffee.chatbot.service.CustomerServiceHandler.Companion  take android.graphics  take com.coffee.chatbot.service  getChatHistorySummaryInternal 1com.coffee.chatbot.service.CustomerServiceHandler  take 1com.coffee.chatbot.service.CustomerServiceHandler  take ;com.coffee.chatbot.service.CustomerServiceHandler.Companion  take kotlin.collections  take kotlin.collections.List  take kotlin.sequences  take kotlin.text  Long android.graphics  Runnable android.graphics  handler android.graphics  !isChatDetailPageMonitoringEnabled android.graphics  monitoringInterval android.graphics  performChatDetailPageCheck android.graphics  
startsWith android.graphics  post android.os.Handler  Long com.coffee.chatbot.service  handler com.coffee.chatbot.service  !isChatDetailPageMonitoringEnabled com.coffee.chatbot.service  monitoringInterval com.coffee.chatbot.service  performChatDetailPageCheck com.coffee.chatbot.service  Long 1com.coffee.chatbot.service.CustomerServiceHandler  Runnable 1com.coffee.chatbot.service.CustomerServiceHandler  chatDetailPageMonitorRunnable 1com.coffee.chatbot.service.CustomerServiceHandler  checkChatContentChanges 1com.coffee.chatbot.service.CustomerServiceHandler  disableChatDetailPageMonitoring 1com.coffee.chatbot.service.CustomerServiceHandler  displayChatSummaryInLog 1com.coffee.chatbot.service.CustomerServiceHandler  handleEnterChatDetailPage 1com.coffee.chatbot.service.CustomerServiceHandler  handleLeaveChatDetailPage 1com.coffee.chatbot.service.CustomerServiceHandler  !isChatDetailPageMonitoringEnabled 1com.coffee.chatbot.service.CustomerServiceHandler  lastChatDetailPageState 1com.coffee.chatbot.service.CustomerServiceHandler  lastChatSummary 1com.coffee.chatbot.service.CustomerServiceHandler  monitoringInterval 1com.coffee.chatbot.service.CustomerServiceHandler  performChatDetailPageCheck 1com.coffee.chatbot.service.CustomerServiceHandler  startChatDetailPageMonitoring 1com.coffee.chatbot.service.CustomerServiceHandler  
startsWith 1com.coffee.chatbot.service.CustomerServiceHandler  stopChatDetailPageMonitoring 1com.coffee.chatbot.service.CustomerServiceHandler  handler ;com.coffee.chatbot.service.CustomerServiceHandler.Companion  !isChatDetailPageMonitoringEnabled ;com.coffee.chatbot.service.CustomerServiceHandler.Companion  monitoringInterval ;com.coffee.chatbot.service.CustomerServiceHandler.Companion  performChatDetailPageCheck ;com.coffee.chatbot.service.CustomerServiceHandler.Companion  
startsWith ;com.coffee.chatbot.service.CustomerServiceHandler.Companion  let java.lang.Runnable  	PageState 1android.accessibilityservice.AccessibilityService  count 1android.accessibilityservice.AccessibilityService  filter 1android.accessibilityservice.AccessibilityService  forEachIndexed 1android.accessibilityservice.AccessibilityService  handleMultiplePages 1android.accessibilityservice.AccessibilityService  
isNotEmpty 1android.accessibilityservice.AccessibilityService  split 1android.accessibilityservice.AccessibilityService  trim 1android.accessibilityservice.AccessibilityService  	PageState android.app.Service  count android.app.Service  filter android.app.Service  forEachIndexed android.app.Service  handleMultiplePages android.app.Service  split android.app.Service  trim android.app.Service  	PageState android.content.Context  count android.content.Context  filter android.content.Context  forEachIndexed android.content.Context  handleMultiplePages android.content.Context  split android.content.Context  trim android.content.Context  	PageState android.content.ContextWrapper  count android.content.ContextWrapper  filter android.content.ContextWrapper  forEachIndexed android.content.ContextWrapper  handleMultiplePages android.content.ContextWrapper  split android.content.ContextWrapper  trim android.content.ContextWrapper  v android.util.Log  	PageState com.coffee.chatbot.service  handleMultiplePages com.coffee.chatbot.service  	PageState 6com.coffee.chatbot.service.ChatbotAccessibilityService  count 6com.coffee.chatbot.service.ChatbotAccessibilityService  $displayChatSummaryInAccessibilityLog 6com.coffee.chatbot.service.ChatbotAccessibilityService  filter 6com.coffee.chatbot.service.ChatbotAccessibilityService  forEachIndexed 6com.coffee.chatbot.service.ChatbotAccessibilityService  handleMultiplePages 6com.coffee.chatbot.service.ChatbotAccessibilityService  
isNotEmpty 6com.coffee.chatbot.service.ChatbotAccessibilityService  lastChatSummary 6com.coffee.chatbot.service.ChatbotAccessibilityService  
lastPageState 6com.coffee.chatbot.service.ChatbotAccessibilityService  split 6com.coffee.chatbot.service.ChatbotAccessibilityService  trim 6com.coffee.chatbot.service.ChatbotAccessibilityService  	PageState @com.coffee.chatbot.service.ChatbotAccessibilityService.Companion  count @com.coffee.chatbot.service.ChatbotAccessibilityService.Companion  filter @com.coffee.chatbot.service.ChatbotAccessibilityService.Companion  forEachIndexed @com.coffee.chatbot.service.ChatbotAccessibilityService.Companion  handleMultiplePages @com.coffee.chatbot.service.ChatbotAccessibilityService.Companion  
isNotEmpty @com.coffee.chatbot.service.ChatbotAccessibilityService.Companion  split @com.coffee.chatbot.service.ChatbotAccessibilityService.Companion  trim @com.coffee.chatbot.service.ChatbotAccessibilityService.Companion  CHAT_DETAIL @com.coffee.chatbot.service.ChatbotAccessibilityService.PageState  CUSTOMER_SERVICE @com.coffee.chatbot.service.ChatbotAccessibilityService.PageState  UNKNOWN @com.coffee.chatbot.service.ChatbotAccessibilityService.PageState  onEnterChatDetailPage 1com.coffee.chatbot.service.CustomerServiceHandler  count kotlin.collections.List  	PageState kotlinx.coroutines  count kotlinx.coroutines  filter kotlinx.coroutines  forEachIndexed kotlinx.coroutines  handleMultiplePages kotlinx.coroutines  
isNotEmpty kotlinx.coroutines  split kotlinx.coroutines  trim kotlinx.coroutines  handleMultiplePages !kotlinx.coroutines.CoroutineScope  FloatingWindowService 1android.accessibilityservice.AccessibilityService  FloatingWindowService android.app.Service  View android.app.Service  View android.content.Context  View android.content.ContextWrapper  BOTTOM android.view.Gravity  GONE android.view.View  VISIBLE android.view.View  
visibility android.view.View  TextView android.widget  append android.widget.TextView  text android.widget.TextView  
log_text_view com.coffee.chatbot.R.id  log_display_layout com.coffee.chatbot.R.layout  TextView com.coffee.chatbot.service  FloatingWindowService 6com.coffee.chatbot.service.ChatbotAccessibilityService  FloatingWindowService @com.coffee.chatbot.service.ChatbotAccessibilityService.Companion  FloatingWindowService 0com.coffee.chatbot.service.FloatingWindowService  String 0com.coffee.chatbot.service.FloatingWindowService  TextView 0com.coffee.chatbot.service.FloatingWindowService  	appendLog 0com.coffee.chatbot.service.FloatingWindowService  createLogWindow 0com.coffee.chatbot.service.FloatingWindowService  handler 0com.coffee.chatbot.service.FloatingWindowService  
hideLogWindow 0com.coffee.chatbot.service.FloatingWindowService  instance 0com.coffee.chatbot.service.FloatingWindowService  logTextView 0com.coffee.chatbot.service.FloatingWindowService  logView 0com.coffee.chatbot.service.FloatingWindowService  
showLogWindow 0com.coffee.chatbot.service.FloatingWindowService  View :com.coffee.chatbot.service.FloatingWindowService.Companion  instance :com.coffee.chatbot.service.FloatingWindowService.Companion  FloatingWindowService kotlinx.coroutines  forEach kotlinx.coroutines  FloatingWindowService !kotlinx.coroutines.CoroutineScope  
FOCUS_DOWN android.view.View  post android.view.View  
ScrollView android.widget  
fullScroll android.widget.ScrollView  post android.widget.ScrollView  log_scroll_view com.coffee.chatbot.R.id  
ScrollView com.coffee.chatbot.service  
ScrollView 0com.coffee.chatbot.service.FloatingWindowService  
logScrollView 0com.coffee.chatbot.service.FloatingWindowService  downTo android.graphics  downTo com.coffee.chatbot.service  downTo 1com.coffee.chatbot.service.CustomerServiceHandler  downTo ;com.coffee.chatbot.service.CustomerServiceHandler.Companion  downTo 
kotlin.Int  CharProgression 
kotlin.ranges  IntProgression 
kotlin.ranges  LongProgression 
kotlin.ranges  UIntProgression 
kotlin.ranges  ULongProgression 
kotlin.ranges  downTo 
kotlin.ranges  
OpenAIService 1android.accessibilityservice.AccessibilityService  Context android.app.Activity  
openAIBaseUrl android.app.Activity  openAIModelName android.app.Activity  openAISystemPrompt android.app.Activity  with android.app.Activity  
OpenAIService android.app.Service  MODE_PRIVATE android.content.Context  
OpenAIService android.content.Context  getSharedPreferences android.content.Context  
openAIBaseUrl android.content.Context  openAIModelName android.content.Context  openAISystemPrompt android.content.Context  with android.content.Context  
OpenAIService android.content.ContextWrapper  getSharedPreferences android.content.ContextWrapper  
openAIBaseUrl android.content.ContextWrapper  openAIModelName android.content.ContextWrapper  openAISystemPrompt android.content.ContextWrapper  with android.content.ContextWrapper  edit !android.content.SharedPreferences  	getString !android.content.SharedPreferences  apply (android.content.SharedPreferences.Editor  	putString (android.content.SharedPreferences.Editor  CoroutineScope android.graphics  Dispatchers android.graphics  
OpenAIService android.graphics  accessibilityService android.graphics  handleMessageInput android.graphics  kotlinx android.graphics  launch android.graphics  
openAIService android.graphics  Context  android.view.ContextThemeWrapper  
openAIBaseUrl  android.view.ContextThemeWrapper  openAIModelName  android.view.ContextThemeWrapper  openAISystemPrompt  android.view.ContextThemeWrapper  with  android.view.ContextThemeWrapper  Context #androidx.activity.ComponentActivity  
openAIBaseUrl #androidx.activity.ComponentActivity  openAIModelName #androidx.activity.ComponentActivity  openAISystemPrompt #androidx.activity.ComponentActivity  with #androidx.activity.ComponentActivity  ScrollState androidx.compose.foundation  rememberScrollState androidx.compose.foundation  verticalScroll androidx.compose.foundation  Context "androidx.compose.foundation.layout  OutlinedTextField "androidx.compose.foundation.layout  fillMaxWidth "androidx.compose.foundation.layout  
openAIBaseUrl "androidx.compose.foundation.layout  openAIModelName "androidx.compose.foundation.layout  openAISystemPrompt "androidx.compose.foundation.layout  with "androidx.compose.foundation.layout  OutlinedTextField .androidx.compose.foundation.layout.ColumnScope  fillMaxWidth .androidx.compose.foundation.layout.ColumnScope  Context androidx.compose.material3  OutlinedTextField androidx.compose.material3  fillMaxWidth androidx.compose.material3  
openAIBaseUrl androidx.compose.material3  openAIModelName androidx.compose.material3  openAISystemPrompt androidx.compose.material3  with androidx.compose.material3  
typography (androidx.compose.material3.MaterialTheme  titleMedium %androidx.compose.material3.Typography  Context androidx.compose.runtime  OutlinedTextField androidx.compose.runtime  fillMaxWidth androidx.compose.runtime  
openAIBaseUrl androidx.compose.runtime  openAIModelName androidx.compose.runtime  openAISystemPrompt androidx.compose.runtime  with androidx.compose.runtime  fillMaxWidth androidx.compose.ui.Modifier  verticalScroll androidx.compose.ui.Modifier  fillMaxWidth &androidx.compose.ui.Modifier.Companion  Context #androidx.core.app.ComponentActivity  
openAIBaseUrl #androidx.core.app.ComponentActivity  openAIModelName #androidx.core.app.ComponentActivity  openAISystemPrompt #androidx.core.app.ComponentActivity  with #androidx.core.app.ComponentActivity  Context com.coffee.chatbot  OutlinedTextField com.coffee.chatbot  fillMaxWidth com.coffee.chatbot  
openAIBaseUrl com.coffee.chatbot  openAIModelName com.coffee.chatbot  openAISystemPrompt com.coffee.chatbot  with com.coffee.chatbot  Context com.coffee.chatbot.MainActivity  getSharedPreferences com.coffee.chatbot.MainActivity  
openAIBaseUrl com.coffee.chatbot.MainActivity  openAIModelName com.coffee.chatbot.MainActivity  openAISystemPrompt com.coffee.chatbot.MainActivity  saveOpenAISettings com.coffee.chatbot.MainActivity  with com.coffee.chatbot.MainActivity  Choice com.coffee.chatbot.service  Gson com.coffee.chatbot.service  OkHttpClient com.coffee.chatbot.service  OpenAIChatRequest com.coffee.chatbot.service  OpenAIChatResponse com.coffee.chatbot.service  
OpenAIMessage com.coffee.chatbot.service  
OpenAIService com.coffee.chatbot.service  Request com.coffee.chatbot.service  Triple com.coffee.chatbot.service  accessibilityService com.coffee.chatbot.service  client com.coffee.chatbot.service  	getApiKey com.coffee.chatbot.service  getSettings com.coffee.chatbot.service  gson com.coffee.chatbot.service  handleMessageInput com.coffee.chatbot.service  kotlinx com.coffee.chatbot.service  
openAIService com.coffee.chatbot.service  toMediaType com.coffee.chatbot.service  
toRequestBody com.coffee.chatbot.service  
OpenAIService 6com.coffee.chatbot.service.ChatbotAccessibilityService  
OpenAIService @com.coffee.chatbot.service.ChatbotAccessibilityService.Companion  message !com.coffee.chatbot.service.Choice  CoroutineScope 1com.coffee.chatbot.service.CustomerServiceHandler  Dispatchers 1com.coffee.chatbot.service.CustomerServiceHandler  
OpenAIService 1com.coffee.chatbot.service.CustomerServiceHandler  handleMessageInput 1com.coffee.chatbot.service.CustomerServiceHandler  kotlinx 1com.coffee.chatbot.service.CustomerServiceHandler  launch 1com.coffee.chatbot.service.CustomerServiceHandler  
openAIService 1com.coffee.chatbot.service.CustomerServiceHandler  AccessibilityService ;com.coffee.chatbot.service.CustomerServiceHandler.Companion  CoroutineScope ;com.coffee.chatbot.service.CustomerServiceHandler.Companion  Dispatchers ;com.coffee.chatbot.service.CustomerServiceHandler.Companion  accessibilityService ;com.coffee.chatbot.service.CustomerServiceHandler.Companion  handleMessageInput ;com.coffee.chatbot.service.CustomerServiceHandler.Companion  kotlinx ;com.coffee.chatbot.service.CustomerServiceHandler.Companion  launch ;com.coffee.chatbot.service.CustomerServiceHandler.Companion  
openAIService ;com.coffee.chatbot.service.CustomerServiceHandler.Companion  choices -com.coffee.chatbot.service.OpenAIChatResponse  content (com.coffee.chatbot.service.OpenAIMessage  Context (com.coffee.chatbot.service.OpenAIService  Dispatchers (com.coffee.chatbot.service.OpenAIService  Gson (com.coffee.chatbot.service.OpenAIService  Log (com.coffee.chatbot.service.OpenAIService  OkHttpClient (com.coffee.chatbot.service.OpenAIService  OpenAIChatRequest (com.coffee.chatbot.service.OpenAIService  OpenAIChatResponse (com.coffee.chatbot.service.OpenAIService  
OpenAIMessage (com.coffee.chatbot.service.OpenAIService  Request (com.coffee.chatbot.service.OpenAIService  Triple (com.coffee.chatbot.service.OpenAIService  client (com.coffee.chatbot.service.OpenAIService  context (com.coffee.chatbot.service.OpenAIService  firstOrNull (com.coffee.chatbot.service.OpenAIService  	getApiKey (com.coffee.chatbot.service.OpenAIService  
getCompletion (com.coffee.chatbot.service.OpenAIService  getSettings (com.coffee.chatbot.service.OpenAIService  gson (com.coffee.chatbot.service.OpenAIService  java (com.coffee.chatbot.service.OpenAIService  listOf (com.coffee.chatbot.service.OpenAIService  toMediaType (com.coffee.chatbot.service.OpenAIService  
toRequestBody (com.coffee.chatbot.service.OpenAIService  use (com.coffee.chatbot.service.OpenAIService  withContext (com.coffee.chatbot.service.OpenAIService  Gson com.google.gson  fromJson com.google.gson.Gson  toJson com.google.gson.Gson  message java.io.IOException  Triple kotlin  use kotlin  with kotlin  toMediaType 
kotlin.String  
component1 
kotlin.Triple  
component2 
kotlin.Triple  
component3 
kotlin.Triple  use 	kotlin.io  
OpenAIService kotlinx.coroutines  AccessibilityService !kotlinx.coroutines.CoroutineScope  OpenAIChatRequest !kotlinx.coroutines.CoroutineScope  OpenAIChatResponse !kotlinx.coroutines.CoroutineScope  
OpenAIMessage !kotlinx.coroutines.CoroutineScope  Request !kotlinx.coroutines.CoroutineScope  accessibilityService !kotlinx.coroutines.CoroutineScope  client !kotlinx.coroutines.CoroutineScope  firstOrNull !kotlinx.coroutines.CoroutineScope  	getApiKey !kotlinx.coroutines.CoroutineScope  getSettings !kotlinx.coroutines.CoroutineScope  gson !kotlinx.coroutines.CoroutineScope  handleMessageInput !kotlinx.coroutines.CoroutineScope  java !kotlinx.coroutines.CoroutineScope  kotlinx !kotlinx.coroutines.CoroutineScope  listOf !kotlinx.coroutines.CoroutineScope  
openAIService !kotlinx.coroutines.CoroutineScope  toMediaType !kotlinx.coroutines.CoroutineScope  
toRequestBody !kotlinx.coroutines.CoroutineScope  use !kotlinx.coroutines.CoroutineScope  IO kotlinx.coroutines.Dispatchers  Call okhttp3  	MediaType okhttp3  OkHttpClient okhttp3  Request okhttp3  RequestBody okhttp3  Response okhttp3  ResponseBody okhttp3  execute okhttp3.Call  toMediaType okhttp3.MediaType.Companion  newCall okhttp3.OkHttpClient  Builder okhttp3.Request  build okhttp3.Request.Builder  header okhttp3.Request.Builder  post okhttp3.Request.Builder  url okhttp3.Request.Builder  
toRequestBody okhttp3.RequestBody.Companion  body okhttp3.Response  code okhttp3.Response  isSuccessful okhttp3.Response  use okhttp3.Response  string okhttp3.ResponseBody  openAIApiKey android.app.Activity  openAIApiKey android.content.Context  openAIApiKey android.content.ContextWrapper  openAIApiKey  android.view.ContextThemeWrapper  openAIApiKey #androidx.activity.ComponentActivity  openAIApiKey "androidx.compose.foundation.layout  openAIApiKey androidx.compose.material3  openAIApiKey androidx.compose.runtime  openAIApiKey #androidx.core.app.ComponentActivity  openAIApiKey com.coffee.chatbot  openAIApiKey com.coffee.chatbot.MainActivity  Quad com.coffee.chatbot.service  T1 com.coffee.chatbot.service  T2 com.coffee.chatbot.service  T3 com.coffee.chatbot.service  T4 com.coffee.chatbot.service  Quad (com.coffee.chatbot.service.OpenAIService  isBlank (com.coffee.chatbot.service.OpenAIService  
component1 com.coffee.chatbot.service.Quad  
component2 com.coffee.chatbot.service.Quad  
component3 com.coffee.chatbot.service.Quad  
component4 com.coffee.chatbot.service.Quad  isBlank !kotlinx.coroutines.CoroutineScope  processChatAndReply 1com.coffee.chatbot.service.CustomerServiceHandler  
startsWith !kotlinx.coroutines.CoroutineScope  	withIndex 0android.view.accessibility.AccessibilityNodeInfo  invoke kotlin.Function0  debugInputAreaStructure 1com.coffee.chatbot.service.CustomerServiceHandler  findAndClickSendButton 1com.coffee.chatbot.service.CustomerServiceHandler  findSendButtonByPosition 1com.coffee.chatbot.service.CustomerServiceHandler  performEnhancedGestureClick 1com.coffee.chatbot.service.CustomerServiceHandler  tryClickSendButtonContainer 1com.coffee.chatbot.service.CustomerServiceHandler  	withIndex &com.coffee.chatbot.service.XPathResult  TimeUnit com.coffee.chatbot.service  TimeUnit (com.coffee.chatbot.service.OpenAIService  TimeUnit java.util.concurrent  SECONDS java.util.concurrent.TimeUnit  Builder okhttp3.OkHttpClient  	Companion okhttp3.OkHttpClient  build okhttp3.OkHttpClient.Builder  connectTimeout okhttp3.OkHttpClient.Builder  readTimeout okhttp3.OkHttpClient.Builder  writeTimeout okhttp3.OkHttpClient.Builder  putInt android.os.BaseBundle  putInt android.os.Bundle  (ACTION_ARGUMENT_MOVEMENT_GRANULARITY_INT 0android.view.accessibility.AccessibilityNodeInfo  ACTION_FOCUS 0android.view.accessibility.AccessibilityNodeInfo  #ACTION_NEXT_AT_MOVEMENT_GRANULARITY 0android.view.accessibility.AccessibilityNodeInfo  ACTION_PASTE 0android.view.accessibility.AccessibilityNodeInfo  MOVEMENT_GRANULARITY_LINE 0android.view.accessibility.AccessibilityNodeInfo  sendEnterMessage 1com.coffee.chatbot.service.CustomerServiceHandler  simulateEnterKeyGesture 1com.coffee.chatbot.service.CustomerServiceHandler  toInt 
kotlin.Double  $findCenterClickableAreaForSendButton 1com.coffee.chatbot.service.CustomerServiceHandler  performSmartClickOnSendButton 1com.coffee.chatbot.service.CustomerServiceHandler  sendEnterMessageWithInputField 1com.coffee.chatbot.service.CustomerServiceHandler  clickSoftKeyboardEnterKey 1com.coffee.chatbot.service.CustomerServiceHandler  findKeyboardEnterKeyByPosition 1com.coffee.chatbot.service.CustomerServiceHandler  at &com.coffee.chatbot.service.XPathResult  	getSystem android.content.res.Resources  	filterNot android.graphics  clickSendButtonWithNativeAPI 1com.coffee.chatbot.service.CustomerServiceHandler  	filterNot 1com.coffee.chatbot.service.CustomerServiceHandler  findSendButtonsRecursively 1com.coffee.chatbot.service.CustomerServiceHandler  findSendImageButtonsRecursively 1com.coffee.chatbot.service.CustomerServiceHandler  	filterNot ;com.coffee.chatbot.service.CustomerServiceHandler.Companion  	filterNot kotlin.collections.MutableList  size kotlin.collections.MutableList  	withIndex kotlin.collections.MutableList  Runtime android.graphics  arrayOf android.graphics  N android.os.Build.VERSION_CODES  Runtime com.coffee.chatbot.service  arrayOf com.coffee.chatbot.service  Runtime 1com.coffee.chatbot.service.CustomerServiceHandler  arrayOf 1com.coffee.chatbot.service.CustomerServiceHandler  performSystemInputTap 1com.coffee.chatbot.service.CustomerServiceHandler  Runtime ;com.coffee.chatbot.service.CustomerServiceHandler.Companion  arrayOf ;com.coffee.chatbot.service.CustomerServiceHandler.Companion  waitFor java.lang.Process  exec java.lang.Runtime  
getRuntime java.lang.Runtime  Array kotlin  arrayOf kotlin  toString 
kotlin.Int  lastSentMessage 1com.coffee.chatbot.service.CustomerServiceHandler  lastSentTime 1com.coffee.chatbot.service.CustomerServiceHandler  sendCooldownMs 1com.coffee.chatbot.service.CustomerServiceHandler  checkMessageSentSuccessfully 1com.coffee.chatbot.service.CustomerServiceHandler  first android.graphics  first com.coffee.chatbot.service  first 1com.coffee.chatbot.service.CustomerServiceHandler  first ;com.coffee.chatbot.service.CustomerServiceHandler.Companion  first kotlin.collections  contains kotlin.collections.MutableList  first kotlin.collections.MutableList  first 
kotlin.ranges  first kotlin.sequences  first kotlin.text  +clickSendButtonWithNativeAPIUsingLatestNode 1com.coffee.chatbot.service.CustomerServiceHandler                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             