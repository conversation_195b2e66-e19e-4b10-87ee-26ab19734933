<?xml version="1.0" encoding="utf-8"?>
<android.widget.FrameLayout>
  <android.widget.LinearLayout>
    <android.widget.FrameLayout>
      <android.widget.LinearLayout resource-id="com.xingin.eva:id/action_bar_root">
        <android.widget.FrameLayout resource-id="android:id/content">
          <android.widget.LinearLayout>
            <androidx.viewpager.widget.ViewPager resource-id="com.xingin.eva:id/homeViewPager" scrollable="true">
              <android.widget.FrameLayout>
                <android.widget.FrameLayout resource-id="com.xingin.eva:id/rootView">
                  <android.widget.FrameLayout>
                    <android.widget.FrameLayout>
                      <android.view.ViewGroup>
                        <android.view.ViewGroup>
                          <android.view.ViewGroup>
                            <android.view.ViewGroup>
                              <android.view.ViewGroup>
                                <android.view.ViewGroup />
                                <android.view.ViewGroup>
                                  <android.view.ViewGroup />
                                  <android.view.ViewGroup>
                                    <android.view.ViewGroup>
                                      <android.view.ViewGroup>
                                        <android.view.ViewGroup />
                                        <android.view.ViewGroup>
                                          <android.view.ViewGroup>
                                            <android.view.ViewGroup clickable="true">
                                              <android.view.ViewGroup>
                                                <android.widget.ImageView />
                                              </android.view.ViewGroup>
                                            </android.view.ViewGroup>
                                            <android.view.ViewGroup>
                                              <android.widget.TextView text="时留咖啡的店" />
                                            </android.view.ViewGroup>
                                            <android.view.ViewGroup clickable="true">
                                              <android.widget.ImageView />
                                            </android.view.ViewGroup>
                                            <android.view.ViewGroup clickable="true">
                                              <android.widget.ImageView />
                                            </android.view.ViewGroup>
                                            <android.view.ViewGroup clickable="true">
                                              <android.widget.ImageView />
                                            </android.view.ViewGroup>
                                          </android.view.ViewGroup>
                                          <android.view.ViewGroup>
                                            <android.widget.ScrollView scrollable="true">
                                              <android.view.ViewGroup>
                                                <android.view.ViewGroup />
                                                <android.view.ViewGroup>
                                                  <android.view.ViewGroup clickable="true">
                                                    <android.widget.TextView text="支付金额" />
                                                    <android.widget.TextView text="¥" />
                                                    <android.widget.TextView text="0.0" />
                                                    <android.widget.TextView />
                                                  </android.view.ViewGroup>
                                                  <android.view.ViewGroup clickable="true">
                                                    <android.widget.TextView text="支付订单数" />
                                                    <android.widget.TextView />
                                                    <android.widget.TextView text="0" />
                                                    <android.widget.TextView />
                                                  </android.view.ViewGroup>
                                                  <android.view.ViewGroup clickable="true">
                                                    <android.widget.TextView text="成功退款金额" />
                                                    <android.widget.TextView text="¥" />
                                                    <android.widget.TextView text="0.0" />
                                                    <android.widget.TextView />
                                                  </android.view.ViewGroup>
                                                  <android.view.ViewGroup clickable="true">
                                                    <android.widget.TextView text="商品访客数" />
                                                    <android.widget.TextView />
                                                    <android.widget.TextView text="9" />
                                                    <android.widget.TextView />
                                                  </android.view.ViewGroup>
                                                  <android.view.ViewGroup clickable="true">
                                                    <android.widget.ImageView />
                                                  </android.view.ViewGroup>
                                                </android.view.ViewGroup>
                                                <android.view.ViewGroup>
                                                  <android.view.ViewGroup>
                                                    <android.view.ViewGroup clickable="true">
                                                      <android.widget.TextView text="待发货" />
                                                      <android.widget.TextView text="1" />
                                                    </android.view.ViewGroup>
                                                    <android.view.ViewGroup clickable="true">
                                                      <android.widget.TextView text="待售后" />
                                                      <android.widget.TextView text="0" />
                                                    </android.view.ViewGroup>
                                                    <android.view.ViewGroup clickable="true">
                                                      <android.widget.TextView text="违规项" />
                                                      <android.widget.TextView text="0" />
                                                    </android.view.ViewGroup>
                                                  </android.view.ViewGroup>
                                                </android.view.ViewGroup>
                                                <android.view.ViewGroup>
                                                  <android.view.ViewGroup>
                                                    <android.view.ViewGroup clickable="true">
                                                      <android.widget.ImageView />
                                                      <android.widget.TextView text="活动报名" />
                                                    </android.view.ViewGroup>
                                                    <android.view.ViewGroup clickable="true">
                                                      <android.widget.ImageView />
                                                      <android.widget.TextView text="广告推广" />
                                                    </android.view.ViewGroup>
                                                    <android.view.ViewGroup clickable="true">
                                                      <android.widget.ImageView />
                                                      <android.widget.TextView text="全部订单" />
                                                    </android.view.ViewGroup>
                                                    <android.view.ViewGroup clickable="true">
                                                      <android.widget.ImageView />
                                                      <android.widget.TextView text="全部工具" />
                                                    </android.view.ViewGroup>
                                                  </android.view.ViewGroup>
                                                </android.view.ViewGroup>
                                                <android.view.ViewGroup>
                                                  <android.view.ViewGroup>
                                                    <android.view.ViewGroup>
                                                      <android.view.ViewGroup clickable="true">
                                                        <android.widget.ImageView />
                                                        <android.widget.TextView text="加速商品销量增长" />
                                                        <android.widget.TextView text="该商品销量周环比下降明显，投广带来更多销量" />
                                                        <android.view.ViewGroup>
                                                          <android.view.ViewGroup>
                                                            <android.widget.TextView text="去推广" />
                                                          </android.view.ViewGroup>
                                                        </android.view.ViewGroup>
                                                      </android.view.ViewGroup>
                                                      <android.view.ViewGroup clickable="true">
                                                        <android.widget.ImageView />
                                                        <android.widget.TextView text="尝试不同笔记内容" />
                                                        <android.widget.TextView text="不同场景的商品介绍会激发用户兴趣" />
                                                        <android.view.ViewGroup>
                                                          <android.view.ViewGroup>
                                                            <android.widget.TextView text="去优化" />
                                                          </android.view.ViewGroup>
                                                        </android.view.ViewGroup>
                                                      </android.view.ViewGroup>
                                                    </android.view.ViewGroup>
                                                    <android.view.ViewGroup clickable="true">
                                                      <android.widget.ImageView />
                                                      <android.widget.TextView text="上架店铺新品" />
                                                      <android.widget.TextView text="提升商品销量" />
                                                      <android.view.ViewGroup>
                                                        <android.view.ViewGroup>
                                                          <android.widget.TextView text="去补充" />
                                                        </android.view.ViewGroup>
                                                      </android.view.ViewGroup>
                                                    </android.view.ViewGroup>
                                                    <android.view.ViewGroup clickable="true">
                                                      <android.widget.ImageView />
                                                      <android.widget.TextView text="为直播商品发笔记" />
                                                      <android.widget.TextView text="发布直播商品的笔记可以提升直播流量" />
                                                      <android.view.ViewGroup>
                                                        <android.view.ViewGroup>
                                                          <android.widget.TextView text="去发布" />
                                                        </android.view.ViewGroup>
                                                      </android.view.ViewGroup>
                                                    </android.view.ViewGroup>
                                                    <android.view.ViewGroup clickable="true">
                                                      <android.widget.ImageView />
                                                      <android.widget.TextView text="优化投放大幅提升广告销量" />
                                                      <android.widget.TextView text="账户余额即将不足，充值避免错过涨销量机会" />
                                                      <android.view.ViewGroup>
                                                        <android.view.ViewGroup>
                                                          <android.widget.TextView text="去充值" />
                                                        </android.view.ViewGroup>
                                                      </android.view.ViewGroup>
                                                    </android.view.ViewGroup>
                                                  </android.view.ViewGroup>
                                                </android.view.ViewGroup>
                                                <android.view.ViewGroup />
                                              </android.view.ViewGroup>
                                            </android.widget.ScrollView>
                                          </android.view.ViewGroup>
                                        </android.view.ViewGroup>
                                        <android.view.ViewGroup>
                                          <android.view.View />
                                          <android.view.ViewGroup clickable="true">
                                            <android.widget.ImageView />
                                            <android.widget.TextView text="客服账号处于“在线”状态" />
                                            <android.widget.TextView text="若无需接待买家，可切换为忙碌状态" />
                                            <android.view.ViewGroup clickable="true">
                                              <android.widget.TextView text="去切换" />
                                            </android.view.ViewGroup>
                                          </android.view.ViewGroup>
                                        </android.view.ViewGroup>
                                      </android.view.ViewGroup>
                                    </android.view.ViewGroup>
                                  </android.view.ViewGroup>
                                </android.view.ViewGroup>
                              </android.view.ViewGroup>
                            </android.view.ViewGroup>
                            <android.view.ViewGroup />
                          </android.view.ViewGroup>
                        </android.view.ViewGroup>
                      </android.view.ViewGroup>
                    </android.widget.FrameLayout>
                  </android.widget.FrameLayout>
                </android.widget.FrameLayout>
              </android.widget.FrameLayout>
              <android.widget.FrameLayout>
                <android.widget.FrameLayout resource-id="com.xingin.eva:id/rootView">
                  <android.widget.FrameLayout>
                    <android.widget.FrameLayout>
                      <android.view.ViewGroup>
                        <android.view.ViewGroup>
                          <android.view.ViewGroup>
                            <android.view.ViewGroup>
                              <android.view.ViewGroup>
                                <android.view.ViewGroup />
                                <android.view.ViewGroup>
                                  <android.view.ViewGroup />
                                  <android.view.ViewGroup>
                                    <android.view.ViewGroup>
                                      <android.view.ViewGroup>
                                        <android.view.ViewGroup />
                                        <android.widget.HorizontalScrollView scrollable="true">
                                          <android.view.ViewGroup>
                                            <android.view.ViewGroup clickable="true">
                                              <android.widget.TextView text="客服接待" />
                                              <android.view.ViewGroup />
                                            </android.view.ViewGroup>
                                            <android.view.ViewGroup clickable="true">
                                              <android.widget.TextView text="通知" />
                                              <android.view.ViewGroup>
                                                <android.widget.TextView text="99+" />
                                              </android.view.ViewGroup>
                                            </android.view.ViewGroup>
                                          </android.view.ViewGroup>
                                        </android.widget.HorizontalScrollView>
                                        <android.view.ViewGroup>
                                          <android.view.ViewGroup>
                                            <android.view.ViewGroup clickable="true">
                                              <android.widget.ImageView />
                                              <android.widget.TextView text="忙碌" />
                                              <android.widget.ImageView />
                                            </android.view.ViewGroup>
                                            <android.view.ViewGroup />
                                            <android.widget.TextView text="排队数" />
                                            <android.widget.TextView text="0" />
                                            <android.widget.TextView text="今日接待" />
                                            <android.widget.TextView text="0" />
                                            <android.widget.ImageView />
                                            <android.widget.ImageView />
                                            <android.widget.ImageView />
                                            <android.view.ViewGroup />
                                          </android.view.ViewGroup>
                                          <android.view.ViewGroup>
                                            <android.widget.TextView text="当前会话" />
                                            <android.widget.TextView text="全部会话" />
                                            <android.widget.TextView text="收藏会话" />
                                          </android.view.ViewGroup>
                                          <android.view.ViewGroup>
                                            <android.widget.ScrollView scrollable="true">
                                              <android.view.ViewGroup>
                                                <android.widget.ImageView />
                                                <android.widget.TextView text="客服工作台会话分类升级说明" />
                                                <android.view.ViewGroup clickable="true">
                                                  <android.widget.TextView text="去查看" />
                                                </android.view.ViewGroup>
                                              </android.view.ViewGroup>
                                            </android.widget.ScrollView>
                                          </android.view.ViewGroup>
                                          <android.view.ViewGroup>
                                            <android.widget.ScrollView>
                                              <android.view.ViewGroup>
                                                <android.view.ViewGroup>
                                                  <android.view.ViewGroup>
                                                    <android.view.ViewGroup>
                                                      <android.view.ViewGroup>
                                                        <android.widget.TextView />
                                                      </android.view.ViewGroup>
                                                      <android.view.ViewGroup clickable="true">
                                                        <android.widget.TextView text="取消收藏" />
                                                      </android.view.ViewGroup>
                                                    </android.view.ViewGroup>
                                                  </android.view.ViewGroup>
                                                  <android.view.ViewGroup>
                                                    <android.view.ViewGroup clickable="true">
                                                      <android.view.ViewGroup clickable="true">
                                                        <android.view.ViewGroup>
                                                          <android.widget.ImageView />
                                                          <android.view.ViewGroup />
                                                        </android.view.ViewGroup>
                                                        <android.view.ViewGroup>
                                                          <android.widget.TextView text="喜八德" />
                                                          <android.widget.TextView text="2025-06-30 07:30" />
                                                          <android.widget.TextView text="会话长时间无新消息，系统关闭会话" />
                                                        </android.view.ViewGroup>
                                                      </android.view.ViewGroup>
                                                    </android.view.ViewGroup>
                                                  </android.view.ViewGroup>
                                                </android.view.ViewGroup>
                                                <android.view.ViewGroup>
                                                  <android.view.ViewGroup>
                                                    <android.view.ViewGroup>
                                                      <android.view.ViewGroup>
                                                        <android.widget.TextView />
                                                      </android.view.ViewGroup>
                                                      <android.view.ViewGroup clickable="true">
                                                        <android.widget.TextView text="收藏" />
                                                      </android.view.ViewGroup>
                                                    </android.view.ViewGroup>
                                                  </android.view.ViewGroup>
                                                  <android.view.ViewGroup>
                                                    <android.view.ViewGroup clickable="true">
                                                      <android.view.ViewGroup clickable="true">
                                                        <android.view.ViewGroup>
                                                          <android.widget.ImageView />
                                                          <android.view.ViewGroup />
                                                        </android.view.ViewGroup>
                                                        <android.view.ViewGroup>
                                                          <android.widget.TextView text="独角兽股权频道" />
                                                          <android.widget.TextView text="2025-06-28 11:25" />
                                                          <android.widget.TextView text="系统自动发送服务评价" />
                                                        </android.view.ViewGroup>
                                                      </android.view.ViewGroup>
                                                    </android.view.ViewGroup>
                                                  </android.view.ViewGroup>
                                                </android.view.ViewGroup>
                                                <android.view.ViewGroup>
                                                  <android.view.ViewGroup>
                                                    <android.view.ViewGroup>
                                                      <android.view.ViewGroup>
                                                        <android.widget.TextView />
                                                      </android.view.ViewGroup>
                                                      <android.view.ViewGroup clickable="true">
                                                        <android.widget.TextView text="收藏" />
                                                      </android.view.ViewGroup>
                                                    </android.view.ViewGroup>
                                                  </android.view.ViewGroup>
                                                  <android.view.ViewGroup>
                                                    <android.view.ViewGroup clickable="true">
                                                      <android.view.ViewGroup clickable="true">
                                                        <android.view.ViewGroup>
                                                          <android.widget.ImageView />
                                                          <android.view.ViewGroup />
                                                        </android.view.ViewGroup>
                                                        <android.view.ViewGroup>
                                                          <android.widget.TextView text="小红薯6625A7E6" />
                                                          <android.widget.TextView text="2025-05-04 11:58" />
                                                          <android.widget.TextView text="会话长时间无新消息，系统关闭会话" />
                                                        </android.view.ViewGroup>
                                                      </android.view.ViewGroup>
                                                    </android.view.ViewGroup>
                                                  </android.view.ViewGroup>
                                                </android.view.ViewGroup>
                                              </android.view.ViewGroup>
                                            </android.widget.ScrollView>
                                          </android.view.ViewGroup>
                                        </android.view.ViewGroup>
                                      </android.view.ViewGroup>
                                    </android.view.ViewGroup>
                                  </android.view.ViewGroup>
                                </android.view.ViewGroup>
                              </android.view.ViewGroup>
                            </android.view.ViewGroup>
                            <android.view.ViewGroup />
                          </android.view.ViewGroup>
                        </android.view.ViewGroup>
                      </android.view.ViewGroup>
                    </android.widget.FrameLayout>
                  </android.widget.FrameLayout>
                </android.widget.FrameLayout>
              </android.widget.FrameLayout>
              <android.widget.LinearLayout resource-id="com.xingin.eva:id/webview_container_v2">
                <android.widget.FrameLayout />
                <android.widget.FrameLayout>
                  <android.widget.FrameLayout>
                    <android.webkit.WebView>
                      <android.webkit.WebView text="数据中心" focused="true">
                        <android.view.View text="" clickable="true">
                          <android.view.View text="" resource-id="app">
                            <android.view.View text="">
                              <android.view.View text="">
                                <android.view.View text="" clickable="true">
                                  <android.view.View text="">
                                    <android.view.View text="">
                                      <android.widget.TextView text="数据中心" />
                                      <android.view.View text="">
                                        <android.widget.TextView text="周报/月报" clickable="true" />
                                      </android.view.View>
                                    </android.view.View>
                                  </android.view.View>
                                </android.view.View>
                                <android.view.View text="" scrollable="true">
                                  <android.view.View text="">
                                    <android.view.View text="">
                                      <android.widget.TabWidget text="" scrollable="true">
                                        <android.view.View text="总览" resource-id="van-tabs-1-0" clickable="true" />
                                        <android.view.View text="商品" resource-id="van-tabs-1-1" clickable="true" />
                                        <android.view.View text="流量" resource-id="van-tabs-1-2" clickable="true" />
                                        <android.view.View text="内容" resource-id="van-tabs-1-3" clickable="true" />
                                        <android.view.View text="店铺" resource-id="van-tabs-1-4" clickable="true" />
                                        <android.view.View text="服务" resource-id="van-tabs-1-5" clickable="true" />
                                        <android.view.View text="市场" resource-id="van-tabs-1-6" clickable="true" />
                                        <android.view.View text="营销" resource-id="van-tabs-1-7" clickable="true" />
                                      </android.widget.TabWidget>
                                    </android.view.View>
                                  </android.view.View>
                                  <android.view.View text="" scrollable="true">
                                    <android.view.View text="">
                                      <android.widget.TextView text="" />
                                      <android.widget.TextView text="经营概览" />
                                      <android.widget.TextView text="更新时间 08:58:14" />
                                      <android.view.View text="" clickable="true">
                                        <android.view.View text="" clickable="true">
                                          <android.widget.TextView text="全部" />
                                          <android.widget.Image text="" />
                                        </android.view.View>
                                      </android.view.View>
                                      <android.view.View text="">
                                        <android.view.View text="">
                                          <android.widget.TextView text="支付金额" />
                                          <android.widget.TextView text="¥0.0" />
                                          <android.widget.TextView text="昨日 ¥85.50" />
                                          <android.view.View text="">
                                            <android.view.View text="">
                                              <android.widget.TextView text="笔记" />
                                              <android.widget.TextView text="¥0.0" />
                                              <android.widget.TextView text="昨日 0" />
                                              <android.widget.TextView text="直播" />
                                              <android.widget.TextView text="¥0.0" />
                                              <android.widget.TextView text="昨日 0" />
                                              <android.widget.TextView text="商卡" />
                                              <android.widget.TextView text="¥0.0" />
                                              <android.widget.TextView text="昨日 85.50" />
                                            </android.view.View>
                                          </android.view.View>
                                          <android.widget.TextView text="支付订单数" />
                                          <android.widget.TextView text="0" />
                                          <android.widget.TextView text="昨日 1" />
                                          <android.widget.TextView text="支付买家数" />
                                          <android.widget.TextView text="0" />
                                          <android.widget.TextView text="昨日 1" />
                                          <android.widget.TextView text="商品访客数" />
                                          <android.widget.TextView text="9" />
                                          <android.widget.TextView text="昨日 113" />
                                        </android.view.View>
                                        <android.view.View text="">
                                          <android.widget.TextView text="成功退款金额" />
                                          <android.widget.TextView text="¥0.0" />
                                          <android.widget.TextView text="昨日 ¥0.00" />
                                          <android.widget.TextView text="支付转化率" />
                                          <android.widget.TextView text="0" />
                                          <android.widget.TextView text="昨日 0.88%" />
                                          <android.widget.TextView text="支付件数" />
                                          <android.widget.TextView text="0" />
                                          <android.widget.TextView text="昨日 1" />
                                          <android.widget.TextView text="加购人数" />
                                          <android.widget.TextView text="0" />
                                          <android.widget.TextView text="昨日 20" />
                                          <android.widget.TextView text="加购件数" />
                                          <android.widget.TextView text="0" />
                                          <android.widget.TextView text="昨日 24" />
                                        </android.view.View>
                                        <android.view.View text="" />
                                      </android.view.View>
                                      <android.widget.TextView text="热销商品" />
                                      <android.widget.TextView text="更新时间 08:58:14" />
                                      <android.view.View text="" clickable="true">
                                        <android.view.View text="" clickable="true">
                                          <android.widget.TextView text="全部" />
                                          <android.widget.Image text="" />
                                        </android.view.View>
                                      </android.view.View>
                                      <android.view.View text="">
                                        <android.widget.GridView text="">
                                          <android.view.View text="">
                                            <android.view.View text="">
                                              <android.view.View text="商品名称" />
                                              <android.view.View text="支付金额" />
                                              <android.view.View text="商品访客数" />
                                            </android.view.View>
                                          </android.view.View>
                                          <android.view.View text="" clickable="true">
                                            <android.view.View text="时留咖啡耶加雪菲沃卡北欧式浅烘焙精品手冲单品咖啡豆可磨粉">
                                              <android.view.View text="" clickable="true">
                                                <android.view.View text="">
                                                  <android.widget.Image text="75" />
                                                </android.view.View>
                                                <android.widget.TextView text="时留咖啡耶加雪菲沃卡北欧式浅烘焙精品手冲单品咖啡豆可磨粉" />
                                              </android.view.View>
                                            </android.view.View>
                                            <android.view.View text="¥0.00">
                                              <android.widget.TextView text="¥0.00" />
                                            </android.view.View>
                                            <android.view.View text="1">
                                              <android.widget.TextView text="1" />
                                            </android.view.View>
                                          </android.view.View>
                                          <android.view.View text="" clickable="true">
                                            <android.view.View text="时留咖啡西江月【小甜豆拼配】中深度烘焙意式拼配咖啡豆可磨粉">
                                              <android.view.View text="" clickable="true">
                                                <android.view.View text="">
                                                  <android.widget.Image text="75" />
                                                </android.view.View>
                                                <android.widget.TextView text="时留咖啡西江月【小甜豆拼配】中深度烘焙意式拼配咖啡豆可磨粉" />
                                              </android.view.View>
                                            </android.view.View>
                                            <android.view.View text="¥0.00">
                                              <android.widget.TextView text="¥0.00" />
                                            </android.view.View>
                                            <android.view.View text="1">
                                              <android.widget.TextView text="1" />
                                            </android.view.View>
                                          </android.view.View>
                                          <android.view.View text="" clickable="true">
                                            <android.view.View text="时留咖啡【超级奶油拼配】深度烘焙意式拼配咖啡豆美式拿铁拉花">
                                              <android.view.View text="" clickable="true">
                                                <android.view.View text="">
                                                  <android.widget.Image text="75" />
                                                </android.view.View>
                                                <android.widget.TextView text="时留咖啡【超级奶油拼配】深度烘焙意式拼配咖啡豆美式拿铁拉花" />
                                              </android.view.View>
                                            </android.view.View>
                                            <android.view.View text="¥0.00">
                                              <android.widget.TextView text="¥0.00" />
                                            </android.view.View>
                                            <android.view.View text="3">
                                              <android.widget.TextView text="3" />
                                            </android.view.View>
                                          </android.view.View>
                                        </android.widget.GridView>
                                      </android.view.View>
                                      <android.widget.TextView text="流量来源" />
                                      <android.widget.TextView text="2025-06-30" />
                                      <android.view.View text="" clickable="true">
                                        <android.view.View text="" clickable="true">
                                          <android.widget.TextView text="全部" />
                                          <android.widget.Image text="" />
                                        </android.view.View>
                                      </android.view.View>
                                      <android.widget.RadioGroup text="">
                                        <android.widget.TextView text="一级载体" />
                                        <android.view.View text="">
                                          <android.view.View text="">
                                            <android.widget.RadioButton text="笔记" clickable="true" />
                                            <android.widget.RadioButton text="直播" clickable="true" />
                                            <android.widget.RadioButton text="商品卡" clickable="true" />
                                          </android.view.View>
                                        </android.view.View>
                                      </android.widget.RadioGroup>
                                      <android.view.View text="">
                                        <android.widget.GridView text="">
                                          <android.view.View text="">
                                            <android.view.View text="">
                                              <android.view.View text="渠道" />
                                              <android.view.View text="商品访客数" />
                                              <android.view.View text="支付金额" />
                                            </android.view.View>
                                          </android.view.View>
                                          <android.view.View text="" clickable="true">
                                            <android.view.View text="发现页">
                                              <android.widget.TextView text="发现页" />
                                            </android.view.View>
                                            <android.view.View text="32 10.34%">
                                              <android.widget.TextView text="32" />
                                              <android.widget.Image text="svg+xml;base64,PHN2ZyB3aWR0aD0iNiIgaGVpZ2h0PSI4IiB2aWV3Qm94PSIwIDAgNiA4IiBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPgo8cGF0aCBkPSJNMC4zNDU2MzEgMi40MTc0M0wyLjU5NTI1IDAuMzc3NDQxQzIuODIwMjUgMC4xODk5NDEgMy4xODc3NSAwLjE4OTk0MSAzLjQxMjc1IDAuMzc3NDQxTDUuNjU0NzYgMi40MDk5NEM2LjAxNDc2IDIuNzE3NDQgNS43NTk3NiAzLjI0MjQ0IDUuMjQ5NzYgMy4yNDI0NEg0LjUwMDI1TDQuMTI1NjMgMy4yNDk5M1Y2Ljk5OTkzQzQuMTI1NjMgNy40MTI0MyAzLjc4ODEzIDcuNzQ5OTMgMy4zNzU2MyA3Ljc0OTkzSDIuNjI1NjNDMi4yMTMxMyA3Ljc0OTkzIDEuODc1NjMgNy40MTI0MyAxLjg3NTYzIDYuOTk5OTNWNS4xNDgwNVYzLjI0OTkzTDEuNTAwMjUgMy4yNDI0NEwwLjc1MDYzIDMuMjQ5OTNDMC4yNDA2MzEgMy4yNDk5MyAtMC4wMTQzNjk1IDIuNzI0OTMgMC4zNDU2MzEgMi40MTc0M1oiIGZpbGw9IiNGRjI0NDIiLz4KPC9zdmc+Cg==" />
                                              <android.widget.TextView text="10.34%" />
                                            </android.view.View>
                                            <android.view.View text="¥0.00 100.00%">
                                              <android.widget.TextView text="¥0.00" />
                                              <android.widget.Image text="svg+xml;base64,PHN2ZyB3aWR0aD0iNiIgaGVpZ2h0PSI4IiB2aWV3Qm94PSIwIDAgNiA4IiBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPgo8cGF0aCBkPSJNNS42NTQzNyA1LjU4MjU3TDMuNDA0NzUgNy42MjI1NkMzLjE3OTc1IDcuODEwMDYgMi44MTIyNSA3LjgxMDA2IDIuNTg3MjUgNy42MjI1NkwwLjM0NTI0MiA1LjU5MDA2Qy0wLjAxNDc1NzcgNS4yODI1NiAwLjI0MDI0MiA0Ljc1NzU2IDAuNzUwMjQyIDQuNzU3NTZIMS40OTk3NUwxLjg3NDM3IDQuNzUwMDdWMS4wMDAwN0MxLjg3NDM3IDAuNTg3NTY5IDIuMjExODcgMC4yNTAwNjkgMi42MjQzNyAwLjI1MDA2OUgzLjM3NDM3QzMuNzg2ODcgMC4yNTAwNjkgNC4xMjQzNyAwLjU4NzU2OSA0LjEyNDM3IDEuMDAwMDdWMi44NTE5NVY0Ljc1MDA3TDQuNDk5NzUgNC43NTc1Nkw1LjI0OTM3IDQuNzUwMDdDNS43NTkzNyA0Ljc1MDA3IDYuMDE0MzcgNS4yNzUwNyA1LjY1NDM3IDUuNTgyNTdaIiBmaWxsPSIjNDJDOUEwIi8+Cjwvc3ZnPgo=" />
                                              <android.widget.TextView text="100.00%" />
                                            </android.view.View>
                                          </android.view.View>
                                          <android.view.View text="" clickable="true">
                                            <android.view.View text="综合搜索页">
                                              <android.widget.TextView text="综合搜索页" />
                                            </android.view.View>
                                            <android.view.View text="12 20.00%">
                                              <android.widget.TextView text="12" />
                                              <android.widget.Image text="svg+xml;base64,PHN2ZyB3aWR0aD0iNiIgaGVpZ2h0PSI4IiB2aWV3Qm94PSIwIDAgNiA4IiBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPgo8cGF0aCBkPSJNMC4zNDU2MzEgMi40MTc0M0wyLjU5NTI1IDAuMzc3NDQxQzIuODIwMjUgMC4xODk5NDEgMy4xODc3NSAwLjE4OTk0MSAzLjQxMjc1IDAuMzc3NDQxTDUuNjU0NzYgMi40MDk5NEM2LjAxNDc2IDIuNzE3NDQgNS43NTk3NiAzLjI0MjQ0IDUuMjQ5NzYgMy4yNDI0NEg0LjUwMDI1TDQuMTI1NjMgMy4yNDk5M1Y2Ljk5OTkzQzQuMTI1NjMgNy40MTI0MyAzLjc4ODEzIDcuNzQ5OTMgMy4zNzU2MyA3Ljc0OTkzSDIuNjI1NjNDMi4yMTMxMyA3Ljc0OTkzIDEuODc1NjMgNy40MTI0MyAxLjg3NTYzIDYuOTk5OTNWNS4xNDgwNVYzLjI0OTkzTDEuNTAwMjUgMy4yNDI0NEwwLjc1MDYzIDMuMjQ5OTNDMC4yNDA2MzEgMy4yNDk5MyAtMC4wMTQzNjk1IDIuNzI0OTMgMC4zNDU2MzEgMi40MTc0M1oiIGZpbGw9IiNGRjI0NDIiLz4KPC9zdmc+Cg==" />
                                              <android.widget.TextView text="20.00%" />
                                            </android.view.View>
                                            <android.view.View text="¥0.00">
                                              <android.widget.TextView text="¥0.00" />
                                              <android.widget.Image text="svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iMTIiIHZpZXdCb3g9IjAgMCAxMiAxMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3QgeD0iMy4wMDAwMyIgeT0iNSIgd2lkdGg9IjYiIGhlaWdodD0iMiIgcng9IjEiIGZpbGw9IiMzMzMzMzMiIGZpbGwtb3BhY2l0eT0iMC4zIi8+Cjwvc3ZnPgo=" />
                                            </android.view.View>
                                          </android.view.View>
                                          <android.view.View text="" clickable="true">
                                            <android.view.View text="消息页">
                                              <android.widget.TextView text="消息页" />
                                            </android.view.View>
                                            <android.view.View text="2 100.00%">
                                              <android.widget.TextView text="2" />
                                              <android.widget.Image text="svg+xml;base64,PHN2ZyB3aWR0aD0iNiIgaGVpZ2h0PSI4IiB2aWV3Qm94PSIwIDAgNiA4IiBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPgo8cGF0aCBkPSJNMC4zNDU2MzEgMi40MTc0M0wyLjU5NTI1IDAuMzc3NDQxQzIuODIwMjUgMC4xODk5NDEgMy4xODc3NSAwLjE4OTk0MSAzLjQxMjc1IDAuMzc3NDQxTDUuNjU0NzYgMi40MDk5NEM2LjAxNDc2IDIuNzE3NDQgNS43NTk3NiAzLjI0MjQ0IDUuMjQ5NzYgMy4yNDI0NEg0LjUwMDI1TDQuMTI1NjMgMy4yNDk5M1Y2Ljk5OTkzQzQuMTI1NjMgNy40MTI0MyAzLjc4ODEzIDcuNzQ5OTMgMy4zNzU2MyA3Ljc0OTkzSDIuNjI1NjNDMi4yMTMxMyA3Ljc0OTkzIDEuODc1NjMgNy40MTI0MyAxLjg3NTYzIDYuOTk5OTNWNS4xNDgwNVYzLjI0OTkzTDEuNTAwMjUgMy4yNDI0NEwwLjc1MDYzIDMuMjQ5OTNDMC4yNDA2MzEgMy4yNDk5MyAtMC4wMTQzNjk1IDIuNzI0OTMgMC4zNDU2MzEgMi40MTc0M1oiIGZpbGw9IiNGRjI0NDIiLz4KPC9zdmc+Cg==" />
                                              <android.widget.TextView text="100.00%" />
                                            </android.view.View>
                                            <android.view.View text="¥0.00">
                                              <android.widget.TextView text="¥0.00" />
                                              <android.widget.Image text="svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iMTIiIHZpZXdCb3g9IjAgMCAxMiAxMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3QgeD0iMy4wMDAwMyIgeT0iNSIgd2lkdGg9IjYiIGhlaWdodD0iMiIgcng9IjEiIGZpbGw9IiMzMzMzMzMiIGZpbGwtb3BhY2l0eT0iMC4zIi8+Cjwvc3ZnPgo=" />
                                            </android.view.View>
                                          </android.view.View>
                                        </android.widget.GridView>
                                      </android.view.View>
                                      <android.widget.TextView text="店铺高流量搜索词为 " />
                                      <android.widget.TextView text="时留咖啡" />
                                      <android.widget.TextView text=" ，访客飙升！ " />
                                      <android.widget.TextView text="去查看" clickable="true" />
                                      <android.widget.TextView text="内容分析" />
                                      <android.widget.TextView text="2025-06-30" />
                                      <android.view.View text="" clickable="true">
                                        <android.view.View text="" clickable="true">
                                          <android.widget.TextView text="全部" />
                                          <android.widget.Image text="" />
                                        </android.view.View>
                                      </android.view.View>
                                      <android.widget.RadioGroup text="">
                                        <android.widget.TextView text="类型" />
                                        <android.view.View text="">
                                          <android.view.View text="">
                                            <android.widget.RadioButton text="笔记" clickable="true" />
                                            <android.widget.RadioButton text="直播" clickable="true" />
                                          </android.view.View>
                                        </android.view.View>
                                      </android.widget.RadioGroup>
                                      <android.view.View text="">
                                        <android.widget.GridView text="">
                                          <android.view.View text="">
                                            <android.view.View text="">
                                              <android.view.View text="笔记信息" />
                                              <android.view.View text="笔记支付金额" />
                                            </android.view.View>
                                          </android.view.View>
                                          <android.view.View text="" clickable="true">
                                            <android.view.View text="咖啡脑袋冲｜多种可选‼️不好喝来打我‼️">
                                              <android.view.View text="" clickable="true">
                                                <android.view.View text="">
                                                  <android.widget.Image text="jpg" />
                                                </android.view.View>
                                                <android.widget.TextView text="咖啡脑袋冲｜多种可选‼️不好喝来打我‼️" />
                                              </android.view.View>
                                            </android.view.View>
                                            <android.view.View text="¥0.00">
                                              <android.widget.TextView text="¥0.00" />
                                              <android.widget.Image text="svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iMTIiIHZpZXdCb3g9IjAgMCAxMiAxMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3QgeD0iMy4wMDAwMyIgeT0iNSIgd2lkdGg9IjYiIGhlaWdodD0iMiIgcng9IjEiIGZpbGw9IiMzMzMzMzMiIGZpbGwtb3BhY2l0eT0iMC4zIi8+Cjwvc3ZnPgo=" />
                                            </android.view.View>
                                          </android.view.View>
                                          <android.view.View text="" clickable="true">
                                            <android.view.View text="精品咖啡豆丨椰子糖🧉">
                                              <android.view.View text="" clickable="true">
                                                <android.view.View text="">
                                                  <android.widget.Image text="jpg" />
                                                </android.view.View>
                                                <android.widget.TextView text="精品咖啡豆丨椰子糖🧉" />
                                              </android.view.View>
                                            </android.view.View>
                                            <android.view.View text="¥0.00">
                                              <android.widget.TextView text="¥0.00" />
                                              <android.widget.Image text="svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iMTIiIHZpZXdCb3g9IjAgMCAxMiAxMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3QgeD0iMy4wMDAwMyIgeT0iNSIgd2lkdGg9IjYiIGhlaWdodD0iMiIgcng9IjEiIGZpbGw9IiMzMzMzMzMiIGZpbGwtb3BhY2l0eT0iMC4zIi8+Cjwvc3ZnPgo=" />
                                            </android.view.View>
                                          </android.view.View>
                                          <android.view.View text="" clickable="true">
                                            <android.view.View text="我的夏日美味灵感丨命运防线——唐·柯里昂">
                                              <android.view.View text="" clickable="true">
                                                <android.view.View text="">
                                                  <android.widget.Image text="jpg" />
                                                </android.view.View>
                                                <android.widget.TextView text="我的夏日美味灵感丨命运防线——唐·柯里昂" />
                                              </android.view.View>
                                            </android.view.View>
                                            <android.view.View text="¥0.00">
                                              <android.widget.TextView text="¥0.00" />
                                              <android.widget.Image text="svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iMTIiIHZpZXdCb3g9IjAgMCAxMiAxMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3QgeD0iMy4wMDAwMyIgeT0iNSIgd2lkdGg9IjYiIGhlaWdodD0iMiIgcng9IjEiIGZpbGw9IiMzMzMzMzMiIGZpbGwtb3BhY2l0eT0iMC4zIi8+Cjwvc3ZnPgo=" />
                                            </android.view.View>
                                          </android.view.View>
                                        </android.widget.GridView>
                                      </android.view.View>
                                      <android.widget.TextView text="服务能力" />
                                      <android.widget.TextView text="2025-05-31~2025-06-29" />
                                      <android.view.View text="" clickable="true">
                                        <android.view.View text="" clickable="true">
                                          <android.widget.TextView text="全部" />
                                          <android.widget.Image text="" />
                                        </android.view.View>
                                      </android.view.View>
                                      <android.view.View text="">
                                        <android.widget.TextView text="物流" clickable="true" />
                                      </android.view.View>
                                      <android.view.View text="">
                                        <android.widget.TextView text="售后" clickable="true" />
                                      </android.view.View>
                                      <android.view.View text="">
                                        <android.view.View text="" clickable="true">
                                          <android.widget.Image text="" />
                                        </android.view.View>
                                      </android.view.View>
                                      <android.view.View text="">
                                        <android.widget.TextView text="客服" clickable="true" />
                                      </android.view.View>
                                      <android.view.View text="">
                                        <android.widget.TextView text="商品评价" clickable="true" />
                                      </android.view.View>
                                      <android.widget.GridView text="">
                                        <android.view.View text="">
                                          <android.view.View text="">
                                            <android.view.View text="物流 支付-发货时长">
                                              <android.widget.TextView text="物流" />
                                              <android.widget.TextView text="支付-发货时长" />
                                            </android.view.View>
                                            <android.view.View text="售后 平均退款时长">
                                              <android.widget.TextView text="售后" />
                                              <android.widget.TextView text="平均退款时长" />
                                            </android.view.View>
                                            <android.view.View text="客服 3分钟回复率">
                                              <android.widget.TextView text="客服" />
                                              <android.widget.TextView text="3分钟回复率" />
                                            </android.view.View>
                                            <android.view.View text="商品 评价平均分">
                                              <android.widget.TextView text="商品" />
                                              <android.widget.TextView text="评价平均分" />
                                            </android.view.View>
                                          </android.view.View>
                                        </android.view.View>
                                        <android.view.View text="">
                                          <android.view.View text="20.98小时 上升 47.64%">
                                            <android.widget.TextView text="20.98小时" />
                                            <android.widget.TextView text="上升 47.64%" />
                                          </android.view.View>
                                          <android.view.View text="1.68小时 上升 43.59%">
                                            <android.widget.TextView text="1.68小时" />
                                            <android.widget.TextView text="上升 43.59%" />
                                          </android.view.View>
                                          <android.view.View text="83.64% 下降 5.00%">
                                            <android.widget.TextView text="83.64%" />
                                            <android.widget.TextView text="下降 5.00%" />
                                          </android.view.View>
                                          <android.view.View text="4.91 下降 1.80%">
                                            <android.widget.TextView text="4.91" />
                                            <android.widget.TextView text="下降 1.80%" />
                                          </android.view.View>
                                        </android.view.View>
                                      </android.widget.GridView>
                                    </android.view.View>
                                  </android.view.View>
                                </android.view.View>
                              </android.view.View>
                            </android.view.View>
                          </android.view.View>
                        </android.view.View>
                      </android.webkit.WebView>
                    </android.webkit.WebView>
                  </android.widget.FrameLayout>
                </android.widget.FrameLayout>
              </android.widget.LinearLayout>
              <android.widget.FrameLayout>
                <android.widget.FrameLayout resource-id="com.xingin.eva:id/rootView">
                  <android.widget.FrameLayout>
                    <android.widget.FrameLayout>
                      <android.view.ViewGroup>
                        <android.view.ViewGroup>
                          <android.view.ViewGroup>
                            <android.view.ViewGroup>
                              <android.view.ViewGroup>
                                <android.view.ViewGroup />
                                <android.view.ViewGroup>
                                  <android.view.ViewGroup />
                                  <android.view.ViewGroup>
                                    <android.view.ViewGroup>
                                      <android.view.ViewGroup>
                                        <android.view.ViewGroup />
                                        <android.view.ViewGroup>
                                          <android.widget.ScrollView>
                                            <android.view.ViewGroup>
                                              <android.view.ViewGroup>
                                                <android.view.ViewGroup clickable="true">
                                                  <android.view.ViewGroup>
                                                    <android.widget.ImageView />
                                                  </android.view.ViewGroup>
                                                </android.view.ViewGroup>
                                                <android.view.ViewGroup clickable="true">
                                                  <android.widget.TextView text="时留咖啡的店" />
                                                </android.view.ViewGroup>
                                                <android.widget.TextView text="店铺ID: 629d958ddf10f2000167a883" />
                                                <android.view.ViewGroup clickable="true">
                                                  <android.widget.ImageView />
                                                </android.view.ViewGroup>
                                                <android.view.ViewGroup>
                                                  <android.view.ViewGroup clickable="true">
                                                    <android.widget.TextView text="店铺信息" />
                                                    <android.widget.TextView text="" />
                                                  </android.view.ViewGroup>
                                                  <android.view.ViewGroup clickable="true">
                                                    <android.widget.TextView text="规则中心" />
                                                    <android.widget.TextView text="" />
                                                  </android.view.ViewGroup>
                                                  <android.view.ViewGroup clickable="true">
                                                    <android.widget.TextView text="商家服务中心" />
                                                    <android.widget.TextView text="" />
                                                  </android.view.ViewGroup>
                                                  <android.view.ViewGroup clickable="true">
                                                    <android.widget.TextView text="问题反馈" />
                                                    <android.widget.TextView text="" />
                                                  </android.view.ViewGroup>
                                                  <android.view.ViewGroup clickable="true">
                                                    <android.widget.TextView text="通知设置" />
                                                    <android.widget.TextView text="" />
                                                  </android.view.ViewGroup>
                                                  <android.view.ViewGroup clickable="true">
                                                    <android.widget.TextView text="检查更新" />
                                                    <android.widget.TextView text="版本 5.9.0" />
                                                    <android.widget.TextView text="" />
                                                  </android.view.ViewGroup>
                                                  <android.view.ViewGroup clickable="true">
                                                    <android.widget.TextView text="关于" />
                                                    <android.widget.TextView text="" />
                                                  </android.view.ViewGroup>
                                                  <android.view.ViewGroup clickable="true">
                                                    <android.widget.TextView text="退出登录" />
                                                  </android.view.ViewGroup>
                                                </android.view.ViewGroup>
                                              </android.view.ViewGroup>
                                            </android.view.ViewGroup>
                                          </android.widget.ScrollView>
                                        </android.view.ViewGroup>
                                      </android.view.ViewGroup>
                                    </android.view.ViewGroup>
                                  </android.view.ViewGroup>
                                </android.view.ViewGroup>
                              </android.view.ViewGroup>
                            </android.view.ViewGroup>
                            <android.view.ViewGroup />
                          </android.view.ViewGroup>
                        </android.view.ViewGroup>
                      </android.view.ViewGroup>
                    </android.widget.FrameLayout>
                  </android.widget.FrameLayout>
                </android.widget.FrameLayout>
              </android.widget.FrameLayout>
            </androidx.viewpager.widget.ViewPager>
            <android.view.View />
            <android.widget.LinearLayout resource-id="com.xingin.eva:id/mTabLayout">
              <android.widget.LinearLayout>
                <android.widget.LinearLayout resource-id="com.xingin.eva:id/mHomeTabView" clickable="true">
                  <android.widget.RelativeLayout>
                    <android.widget.RelativeLayout>
                      <android.widget.LinearLayout>
                        <android.widget.ImageView resource-id="com.xingin.eva:id/mIcon" />
                        <android.widget.TextView text="首页" resource-id="com.xingin.eva:id/mTitle" />
                      </android.widget.LinearLayout>
                    </android.widget.RelativeLayout>
                  </android.widget.RelativeLayout>
                </android.widget.LinearLayout>
                <android.widget.LinearLayout resource-id="com.xingin.eva:id/mMessageTabView" clickable="true">
                  <android.widget.RelativeLayout>
                    <android.widget.RelativeLayout>
                      <android.widget.LinearLayout>
                        <android.widget.ImageView resource-id="com.xingin.eva:id/mIcon" />
                        <android.widget.TextView text="消息" resource-id="com.xingin.eva:id/mTitle" />
                      </android.widget.LinearLayout>
                      <android.widget.TextView text="99+" resource-id="com.xingin.eva:id/mUnReadNum" />
                    </android.widget.RelativeLayout>
                  </android.widget.RelativeLayout>
                </android.widget.LinearLayout>
                <android.widget.LinearLayout resource-id="com.xingin.eva:id/mDataTabView" clickable="true">
                  <android.widget.RelativeLayout>
                    <android.widget.RelativeLayout>
                      <android.widget.LinearLayout>
                        <android.widget.ImageView resource-id="com.xingin.eva:id/mIcon" />
                        <android.widget.TextView text="数据" resource-id="com.xingin.eva:id/mTitle" />
                      </android.widget.LinearLayout>
                    </android.widget.RelativeLayout>
                  </android.widget.RelativeLayout>
                </android.widget.LinearLayout>
                <android.widget.LinearLayout resource-id="com.xingin.eva:id/mMeTabView" clickable="true">
                  <android.widget.RelativeLayout>
                    <android.widget.RelativeLayout>
                      <android.widget.LinearLayout>
                        <android.widget.ImageView resource-id="com.xingin.eva:id/mIcon" />
                        <android.widget.TextView text="我的" resource-id="com.xingin.eva:id/mTitle" />
                      </android.widget.LinearLayout>
                    </android.widget.RelativeLayout>
                  </android.widget.RelativeLayout>
                </android.widget.LinearLayout>
              </android.widget.LinearLayout>
            </android.widget.LinearLayout>
          </android.widget.LinearLayout>
          <android.widget.FrameLayout clickable="true">
            <android.widget.FrameLayout>
              <android.widget.FrameLayout resource-id="com.xingin.eva:id/view_expand">
                <android.widget.LinearLayout>
                  <android.widget.LinearLayout resource-id="com.xingin.eva:id/v_customer_service">
                    <android.widget.FrameLayout>
                      <android.widget.ImageView resource-id="com.xingin.eva:id/icon" />
                    </android.widget.FrameLayout>
                    <android.widget.TextView text="客服" resource-id="com.xingin.eva:id/tv_customer_service_title" />
                  </android.widget.LinearLayout>
                  <android.view.View resource-id="com.xingin.eva:id/v_line_second" />
                  <android.widget.LinearLayout resource-id="com.xingin.eva:id/v_i_want_feedback">
                    <android.widget.ImageView />
                    <android.widget.TextView text="反馈" resource-id="com.xingin.eva:id/tv_i_want_feedback_title" />
                  </android.widget.LinearLayout>
                  <android.widget.FrameLayout resource-id="com.xingin.eva:id/btn_collapse">
                    <android.widget.ImageView />
                  </android.widget.FrameLayout>
                </android.widget.LinearLayout>
              </android.widget.FrameLayout>
            </android.widget.FrameLayout>
          </android.widget.FrameLayout>
        </android.widget.FrameLayout>
      </android.widget.LinearLayout>
    </android.widget.FrameLayout>
  </android.widget.LinearLayout>
  <android.view.View resource-id="android:id/navigationBarBackground" />
</android.widget.FrameLayout>