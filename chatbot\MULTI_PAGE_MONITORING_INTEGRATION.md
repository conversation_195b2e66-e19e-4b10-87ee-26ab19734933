# 多页面监听集成指南

## 概述

本指南说明如何在现有的 `ChatbotAccessibilityService` 中集成多页面监听功能，同时监听"客服接待"页面和"会话详情"页面，并根据不同页面状态做出相应处理。

## 集成方式

### 1. 在现有协程中集成

我们已经将多页面监听功能集成到现有的 `onAccessibilityEvent` 协程中：

```kotlin
coroutineScope.launch {
    try {
        if (isExtractionRunning) {
            Log.d(TAG, "Starting extraction from event")
            
            // 页面处理需要在主线程执行（因为涉及UI操作）
            withContext(Dispatchers.Main) {
                // 同时监听两个页面：客服接待页面和会话详情页
                handleMultiplePages()
            }

            // 聊天历史提取可以在后台线程执行
            withContext(Dispatchers.Default) {
                extractChatHistory(nodeInfo)
            }
            
            // 更新WebViewService中的节点信息
            val webViewService = WebViewService.getInstance()
            webViewService?.updateRootNode(rootInActiveWindow)
        }
    } catch (e: Exception) {
        Log.e(TAG, "Error processing accessibility event", e)
    }
}
```

### 2. 多页面处理逻辑

新的 `handleMultiplePages()` 方法实现了智能的页面检测和处理：

```kotlin
private fun handleMultiplePages() {
    // 1. 首先检查是否在会话详情页
    val chatDetailResult = handler.onEnterChatDetailPage()
    
    if (chatDetailResult.isInChatDetailPage) {
        // 处理会话详情页逻辑
        handleChatDetailPage(chatDetailResult)
        return
    }
    
    // 2. 如果不在会话详情页，检查客服接待页面
    val isInCustomerServicePage = handler.isInCustomerServicePage()
    if (isInCustomerServicePage) {
        // 处理客服接待页面逻辑
        handleCustomerServicePage()
    }
}
```

## 功能特性

### 1. 智能页面检测

- **优先级检测**: 优先检测会话详情页，避免冲突
- **状态变化检测**: 只在页面状态发生变化时触发相应处理
- **内容变化检测**: 检测会话内容的变化，避免重复显示

### 2. 页面状态管理

```kotlin
private enum class PageState {
    UNKNOWN,           // 未知页面
    CUSTOMER_SERVICE,  // 客服接待页面
    CHAT_DETAIL       // 会话详情页
}
```

### 3. 会话内容监控

- **自动获取概要**: 进入会话详情页时自动获取会话概要
- **内容变化检测**: 检测会话内容更新，实时显示新消息
- **详细日志输出**: 在日志中显示完整的会话记录

## 日志输出示例

### 进入会话详情页时
```
D/ChatbotAccessibility: 🎯 === 页面状态变化：进入会话详情页 ===
D/ChatbotAccessibility: 🔄 === 会话内容发生变化 ===
D/ChatbotAccessibility: 📋 === 会话记录概要 ===
D/ChatbotAccessibility: 会话记录总行数: 12
D/ChatbotAccessibility: [0] - 客户: 你好，我想咨询一下产品信息
D/ChatbotAccessibility: [1] - 客服: 您好！很高兴为您服务
D/ChatbotAccessibility: [2] - 客户: 这个产品的价格是多少？
D/ChatbotAccessibility: [3] - 客服: 产品价格是299元
D/ChatbotAccessibility: 📊 统计信息: 共 4 条消息记录
D/ChatbotAccessibility: 📈 消息分析: 客户消息 2 条, 客服消息 2 条
D/ChatbotAccessibility: 📋 === 会话记录概要结束 ===
```

### 进入客服接待页面时
```
D/ChatbotAccessibility: 🏢 === 页面状态变化：进入客服接待页面 ===
D/ChatbotAccessibility: 🏢 检测客服接待页面，检查未读消息...
D/ChatbotAccessibility: ✅ 客服接待页面 - 成功点击进入会话
```

### 会话内容更新时
```
D/ChatbotAccessibility: 🔄 === 会话内容发生变化 ===
D/ChatbotAccessibility: 📋 === 会话记录概要 ===
D/ChatbotAccessibility: [4] - 客户: 有优惠活动吗？
D/ChatbotAccessibility: [5] - 客服: 现在有8折优惠活动
D/ChatbotAccessibility: 📊 统计信息: 共 6 条消息记录
D/ChatbotAccessibility: 📈 消息分析: 客户消息 3 条, 客服消息 3 条
D/ChatbotAccessibility: 📋 === 会话记录概要结束 ===
```

## 性能优化

### 1. 避免重复处理
- 只在页面状态变化时触发处理
- 只在会话内容变化时显示概要
- 使用状态缓存避免重复检查

### 2. 资源管理
- 自动管理 AccessibilityNodeInfo 资源
- 在适当的线程中执行不同类型的操作
- 异常处理确保服务稳定性

### 3. 日志级别控制
- 使用不同级别的日志输出
- 可以通过调整日志级别控制输出详细程度

## 与原有功能的兼容性

### 1. 保持原有接口
- 原有的 `handleCustomerServicePage()` 方法仍然保留
- 原有的客服接待页面处理逻辑不变
- 新增功能不影响现有功能

### 2. 渐进式集成
- 可以选择使用新的 `handleMultiplePages()` 或保持原有方式
- 支持逐步迁移到新的多页面监听方式

## 使用建议

### 1. 启用多页面监听
```kotlin
// 在 onAccessibilityEvent 中使用新的多页面处理
withContext(Dispatchers.Main) {
    handleMultiplePages()  // 替代原来的 handleCustomerServicePage()
}
```

### 2. 监控日志输出
- 关注页面状态变化日志
- 监控会话概要获取情况
- 根据需要调整日志级别

### 3. 异常处理
- 新功能包含完整的异常处理
- 异常不会影响原有的聊天历史提取功能
- 可以独立启用/禁用不同功能

## 扩展可能

### 1. 添加更多页面类型
可以轻松扩展支持更多页面类型：
```kotlin
private enum class PageState {
    UNKNOWN,
    CUSTOMER_SERVICE,
    CHAT_DETAIL,
    SETTINGS,        // 设置页面
    PROFILE         // 个人资料页面
}
```

### 2. 自定义处理逻辑
可以为不同页面添加自定义的处理逻辑：
```kotlin
when (currentPageState) {
    PageState.CHAT_DETAIL -> handleChatDetailPage()
    PageState.CUSTOMER_SERVICE -> handleCustomerServicePage()
    PageState.SETTINGS -> handleSettingsPage()
    // ...
}
```

这种集成方式让您可以在现有的 AccessibilityService 架构中无缝添加多页面监听功能，同时保持代码的清晰性和可维护性。
