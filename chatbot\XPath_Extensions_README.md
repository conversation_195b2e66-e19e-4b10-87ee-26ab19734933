# AccessibilityNodeInfo XPath 扩展功能

## 概述

本扩展为 Android AccessibilityNodeInfo 提供了优雅简洁的 XPath 查询功能，参考了 Python lxml 库的设计理念，提供流畅的链式操作和便捷的查找方法。

## 主要特性

### 🚀 优雅的 API 设计
- **链式操作**: 支持流畅的方法链调用
- **便捷方法**: 提供简化的查找语法
- **CSS 选择器**: 支持类似 CSS 的选择器语法
- **自动资源管理**: 智能的内存管理和资源回收

### 🎯 强大的查询功能
- **完整 XPath 支持**: 支持标准 XPath 语法
- **属性匹配**: 支持多种属性匹配操作符
- **位置谓词**: 支持 position()、last() 等函数
- **逻辑组合**: 支持 and、or、not 逻辑操作

## 基本用法

### 1. 传统 XPath 查询

```kotlin
// 查找所有可点击的按钮
val buttons = rootNode.xpath("//Button[@clickable='true']")

// 查找第一个文本包含"消息"的 TextView
val messageView = rootNode.xpath("//TextView[@text~='消息']").first()

// 链式操作：找到确定按钮的父容器中的所有按钮
rootNode.xpath("//TextView[@text='确定']")
        .parent()
        .xpath(".//Button[@clickable='true']")
        .click()
```

### 2. 便捷查找方法

```kotlin
// 根据属性查找单个节点
val sendButton = rootNode.find(
    className = "Button",
    text = "发送",
    clickable = true
)

// 查找所有匹配的节点
val allButtons = rootNode.findAll(
    className = "Button", 
    clickable = true
)

// 根据文本查找
val exactMatch = rootNode.findByText("确定", exact = true)
val partialMatch = rootNode.findByText("消息", exact = false)

// 根据 ID 查找
val messageInput = rootNode.findById("message_input")
```

### 3. CSS 选择器风格

```kotlin
// 类选择器风格
val clickableButtons = rootNode.select("Button.clickable")
val enabledTextViews = rootNode.select("TextView.enabled")

// ID 选择器风格
val messageBox = rootNode.select("#message_input")

// 组合选择器
val clickableTextViews = rootNode.select("TextView.clickable.enabled")
```

## 高级功能

### 1. 安全的资源管理

```kotlin
// 自动资源回收
rootNode.xpathUse("//Button[@text='发送']") { result ->
    result.first()?.performAction(AccessibilityNodeInfo.ACTION_CLICK)
}

// 手动资源管理
val result = rootNode.xpath("//TextView")
try {
    // 使用结果...
    result.forEach { node ->
        println(node.text)
    }
} finally {
    result.recycle() // 回收资源
}
```

### 2. 调试和诊断

```kotlin
// 获取节点的完整 XPath 路径
val button = rootNode.findByText("发送")
val xpath = button?.getXPath()
println("按钮的 XPath: $xpath")

// 获取节点描述
val description = button?.describe()
println("按钮描述: $description")

// 打印节点树结构
rootNode.printTree(maxDepth = 3)
```

### 3. 复杂查询示例

```kotlin
// 查找有未读消息的会话
val unreadConversations = rootNode.xpath(
    "//ViewGroup[.//TextView[@text~='未读']]"
)

// 使用位置谓词
val firstThreeItems = rootNode.xpath("//ViewGroup[position()<=3]")
val lastItem = rootNode.xpath("//ViewGroup[last()]")

// 复杂条件组合
val complexQuery = rootNode.xpath(
    "//ViewGroup[.//TextView[@text~='客服'] and .//TextView[@text~='在线']]"
)
```

## XPath 语法支持

### 基本路径
- `/` - 直接子节点
- `//` - 后代节点
- `..` - 父节点
- `.` - 当前节点

### 属性谓词
- `[@attr='value']` - 精确匹配
- `[@attr!='value']` - 不等于
- `[@attr~='value']` - 包含子字符串
- `[@attr^='value']` - 以...开头
- `[@attr$='value']` - 以...结尾
- `[@attr?='regex']` - 正则表达式匹配

### 位置谓词
- `[0]`, `[1]` - 索引选择
- `[position()=n]` - 位置等于
- `[position()>n]` - 位置大于
- `[last()]` - 最后一个
- `[last()-n]` - 倒数第n个

### 逻辑操作
- `[condition1 and condition2]` - 与操作
- `[condition1 or condition2]` - 或操作
- `[not(condition)]` - 非操作

## 支持的属性

| 属性名 | 说明 | 示例 |
|--------|------|------|
| text | 文本内容 | `[@text='确定']` |
| id | 资源ID | `[@id='button_send']` |
| desc | 内容描述 | `[@desc='发送按钮']` |
| pkg | 包名 | `[@pkg='com.example.app']` |
| cls | 类名 | `[@cls='Button']` |
| clickable | 可点击 | `[@clickable='true']` |
| scrollable | 可滚动 | `[@scrollable='true']` |
| checked | 已选中 | `[@checked='true']` |
| selected | 已选择 | `[@selected='true']` |
| enabled | 已启用 | `[@enabled='true']` |
| focused | 已聚焦 | `[@focused='true']` |
| visible | 可见 | `[@visible='true']` |

## 性能优化建议

### 1. 使用具体路径
```kotlin
// 好：使用具体路径
val specificPath = rootNode.xpath("/ViewGroup[0]/ScrollView[0]//TextView")

// 避免：全局搜索
val globalSearch = rootNode.xpath("//TextView")
```

### 2. 限制搜索范围
```kotlin
// 先找到容器，再在容器内搜索
val container = rootNode.findById("conversation_container")
container?.let {
    val messages = it.xpath(".//TextView[@text~='消息']")
}
```

### 3. 批量操作
```kotlin
// 一次查找，批量处理
val allButtons = rootNode.findAll(className = "Button", clickable = true)
allButtons.forEach { button ->
    // 批量处理
}
```

## 向后兼容性

为了保持向后兼容，原有的方法仍然可用：

```kotlin
// 原有方法仍然支持
val node = rootNode.findNodeByXPath("//Button")
val nodes = rootNode.findNodesByXPath("//Button")
val exists = rootNode.existsByXPath("//Button")
val clicked = rootNode.clickByXPath("//Button")
```

## 实际应用示例

### 客服系统自动化
```kotlin
// 查找客服会话
fun AccessibilityNodeInfo.findCustomerServiceConversations(): XPathResult {
    return xpath("//ViewGroup[.//TextView[@text~='客服']]")
}

// 查找未读消息
fun AccessibilityNodeInfo.findUnreadMessages(): XPathResult {
    return xpath("//ViewGroup[.//TextView[@text~='未读'] or .//View[@desc~='未读']]")
}

// 自动回复流程
val unreadConversations = rootNode.findUnreadMessages()
unreadConversations.first()?.performAction(AccessibilityNodeInfo.ACTION_CLICK)

val inputField = rootNode.find(className = "EditText", enabled = true)
// 输入回复内容...

val sendButton = rootNode.findSendButton()
sendButton?.performAction(AccessibilityNodeInfo.ACTION_CLICK)
```

## 注意事项

1. **资源管理**: 记得调用 `recycle()` 回收不再使用的节点
2. **空值检查**: 查找方法可能返回 null，使用前请检查
3. **性能考虑**: 复杂的 XPath 查询可能影响性能，建议使用具体路径
4. **正则表达式**: 使用 `[@attr?='regex']` 时注意正则表达式的正确性

## 总结

优化后的 XPath 扩展功能提供了：
- 🎯 **简洁优雅**的 API 设计
- ⚡ **高性能**的查询实现  
- 🔗 **流畅的链式**操作体验
- 🛡️ **安全的资源**管理机制
- 🔧 **丰富的便捷**方法支持

让 Android 无障碍服务开发变得更加简单高效！
